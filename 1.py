def get_pdf_file_paths(plant_name, base_dir="downloads"):
    folder_name = plant_name.replace(" ", "_").replace('"', '')
    plant_dir = Path("downloads") / folder_name
 
    if not os.path.isdir(plant_dir):
        raise FileNotFoundError(f"Directory not found: {plant_dir}")
 
    pdf_paths = [
    str(file_path)
    for file_path in plant_dir.glob("*.pdf")]
 
    
    return pdf_paths
 
from langchain.document_loaders import UnstructuredPDFLoader
 
def extract_first_5_pages_text_langchain(pdf_path, max_pages=5, languages=["eng"]):
    try:
        loader = UnstructuredPDFLoader(
            file_path=pdf_path,
            strategy="auto",  # auto-detect scanned or digital
            languages=["eng"]
        )
        documents = loader.load()
 
        # Combine content of only first N pages
        text = ""
        for i, doc in enumerate(documents):
            if i >= max_pages:
                break
            text += doc.page_content + "\n"
 
        return text.strip()
 
    except Exception as e:
        print(f"Error extracting {pdf_path}: {e}")
        return ""
 
from openai import OpenAI
from dotenv import load_dotenv
import os
 
# Load API key from .env
load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
 
def is_valid_annual_report(text, plant_name, model="gpt-4o"):
    system_prompt = (
        "You are a document classification assistant. Your task is to check whether a given document "
        "is an annual report and whether it belongs to the specified power plant. Additionally, determine "
        "what type of financial reports are included (standalone, consolidated, or both)."
    )

    user_prompt = f"""
    You are given the beginning of a document. Please answer YES or NO only for each question.

    1. Is this document an *annual report*?
    2. Does this annual report belong to the power plant named "{plant_name}"?
    3. Does the document include *standalone* financial reports?
    4. Does the document include *consolidated* financial reports?

    Respond strictly in this format:
    Annual report: YES or NO along with the reason
    Belongs to plant: YES or NO along with the reason
    Has standalone: YES or NO along with the reason
    Has consolidated: YES or NO along with the reason

    Here is the text:
    {text[:100000]}

    NOTE: The power plant name may be abbreviated or phrased differently. Use judgment in matching it.
    """

    try:
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0
        )

        content = response.choices[0].message.content
        print("🔍 GPT-4o Reasoning:\n" + content.strip() + "\n")

        # Convert to lowercase for consistent checks
        content_lower = content.lower()

        # Extract flags
        is_annual = "annual report: yes" in content_lower
        is_correct_plant = "belongs to plant: yes" in content_lower
        has_standalone = "has standalone: yes" in content_lower
        has_consolidated = "has consolidated: yes" in content_lower

        # Decision logic: Keep if it's an annual report for the correct plant AND has standalone OR consolidated
        is_valid_report = is_annual and is_correct_plant and (has_standalone or has_consolidated)

        # Print decision reasoning
        if is_valid_report:
            report_type = []
            if has_standalone:
                report_type.append("standalone")
            if has_consolidated:
                report_type.append("consolidated")
            print(f"✅ KEEPING: Valid annual report with {' and '.join(report_type)} financials")
        else:
            reasons = []
            if not is_annual:
                reasons.append("not an annual report")
            if not is_correct_plant:
                reasons.append("wrong plant")
            if not (has_standalone or has_consolidated):
                reasons.append("no financial reports found")
            print(f"❌ REJECTING: {', '.join(reasons)}")

        return is_valid_report

    except Exception as e:
        print(f"❌ Error verifying report: {e}")
        return False
 
import os
 
def delete_if_invalid(flag: bool, pdf_path: str):
    if flag:
        print(f"✅ Keeping: {pdf_path}")
        return True
    else:
        try:
            os.remove(pdf_path)
            print(f"🗑️ Deleted: {pdf_path}")
            return False
        except Exception as e:
            print(f"❌ Failed to delete {pdf_path}: {e}")
            return False
 
 
def process_pdf_list_with_validation(pdf_list, plant_name: str, model: str = "gpt-4o", max_pages: int = 5):
    for each_pdf in pdf_list:
        print(f"Processing: {each_pdf}")
        text = extract_first_5_pages_text_langchain(each_pdf, max_pages=max_pages)
        validation_flag = is_valid_annual_report(text, plant_name, model=model)
        print(f"Validation result for {each_pdf}: {validation_flag}")
        delete_if_invalid(validation_flag, each_pdf)
 
pdf_list = get_pdf_file_paths(plant_name)
process_pdf_list_with_validation(pdf_list,plant_name)
 