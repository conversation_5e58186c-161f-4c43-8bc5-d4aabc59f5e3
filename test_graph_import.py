#!/usr/bin/env python3
"""
Test script to replicate the exact import pattern from graph.py
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path (same as in the main code)
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

load_dotenv()

def test_graph_imports():
    """Test the exact import pattern from graph.py"""
    print("🔍 Testing graph.py import pattern...")
    
    try:
        # Replicate the exact imports from graph.py
        print("📍 Step 1: Importing basic modules...")
        from agent.tools_and_schemas import SearchQueryList, Reflection, PowerPlantReflection
        from langchain_core.messages import AIMessage
        from langgraph.types import Send
        from langgraph.graph import StateGraph
        from langgraph.graph import START, END
        from langchain_core.runnables import RunnableConfig
        print("✅ Basic imports successful")
        
        print("📍 Step 2: Importing Google Generative AI...")
        try:
            from google.genai import Client
        except ImportError:
            # Fallback for different google-genai versions
            try:
                import google.generativeai as genai
                Client = genai
            except ImportError:
                print("⚠️ Warning: Google Generative AI client not available")
                return False
        print("✅ Google Generative AI import successful")
        
        print("📍 Step 3: Importing agent modules...")
        from agent.state import (
            OverallState,
            QueryGenerationState,
            ReflectionState,
            WebSearchState,
        )
        from agent.configuration import Configuration
        from agent.prompts import (
            get_current_date,
            query_writer_instructions,
            web_searcher_instructions,
            reflection_instructions,
            answer_instructions,
            power_plant_query_instructions,
            holding_company_search_instructions,
            power_plant_web_search_instructions,
            power_plant_reflection_instructions,
            power_plant_final_answer_instructions,
        )
        from langchain_google_genai import ChatGoogleGenerativeAI
        from agent.utils import (
            get_citations,
            get_research_topic,
            insert_citation_markers,
            resolve_urls,
        )
        print("✅ Agent module imports successful")
        
        print("📍 Step 4: Testing client initialization...")
        if os.getenv("GEMINI_API_KEY") is None:
            print("❌ GEMINI_API_KEY is not set")
            return False
        
        # Test the exact client initialization pattern from graph.py
        try:
            # Try newer version of google.genai
            if hasattr(Client, '__call__'):
                # Client is a class, instantiate it
                genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))
            else:
                # Client is a module, configure it
                Client.configure(api_key=os.getenv("GEMINI_API_KEY"))
                genai_client = Client
            print("✅ Client initialization successful")
            
        except TypeError as e:
            if "proxies" in str(e) or "unexpected keyword argument" in str(e):
                print(f"❌ Client initialization failed with proxies error: {e}")
                return False
            else:
                print(f"❌ Client initialization failed: {e}")
                return False
        except Exception as e:
            print(f"❌ Client initialization failed: {e}")
            return False
        
        print("📍 Step 5: Testing graph import...")
        from agent.graph import power_plant_graph
        print("✅ Graph import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_comprehensive_scraper_import():
    """Test importing the comprehensive scraper"""
    print("\n🔍 Testing comprehensive scraper import...")
    
    try:
        from agent.comprehensive_automated_scraper import ComprehensiveAutomatedScraper
        print("✅ Comprehensive scraper import successful")
        
        # Test initialization
        scraper = ComprehensiveAutomatedScraper(download_dir="./test_downloads")
        print("✅ Comprehensive scraper initialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive scraper import/init failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("🔧 GRAPH IMPORT DIAGNOSTIC TOOL")
    print("=" * 60)
    
    # Test graph imports
    graph_success = test_graph_imports()
    
    # Test comprehensive scraper
    scraper_success = test_comprehensive_scraper_import()
    
    print("\n" + "=" * 60)
    print("📋 RESULTS:")
    
    if graph_success and scraper_success:
        print("✅ All imports successful!")
        print("   The issue might be in the actual execution, not imports.")
    else:
        print("❌ Import issues found:")
        if not graph_success:
            print("   - Graph imports failed")
        if not scraper_success:
            print("   - Comprehensive scraper imports failed")


if __name__ == "__main__":
    main()
