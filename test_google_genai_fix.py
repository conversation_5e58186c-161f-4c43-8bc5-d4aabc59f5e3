#!/usr/bin/env python3
"""
Test script to diagnose and fix the Google Generative AI client issue.

This script tests different ways to initialize the Google Generative AI client
to resolve the "Client.__init__() got an unexpected keyword argument 'proxies'" error.
"""

import os
from dotenv import load_dotenv

load_dotenv()

def test_google_genai_client():
    """Test different ways to initialize the Google Generative AI client."""
    print("🔍 Testing Google Generative AI client initialization...")
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY not found in environment variables")
        return None
    
    print(f"✅ Found GEMINI_API_KEY: {api_key[:10]}...")
    
    # Method 1: Try google.genai.Client
    try:
        print("\n📍 Method 1: Trying google.genai.Client...")
        from google.genai import Client
        client = Client(api_key=api_key)
        print("✅ Method 1 SUCCESS: google.genai.Client with api_key parameter")
        return client
    except TypeError as e:
        if "proxies" in str(e) or "unexpected keyword argument" in str(e):
            print(f"❌ Method 1 FAILED: {e}")
            print("   This is the error we're trying to fix!")
        else:
            print(f"❌ Method 1 FAILED: {e}")
    except ImportError as e:
        print(f"❌ Method 1 FAILED: Import error - {e}")
    except Exception as e:
        print(f"❌ Method 1 FAILED: {e}")
    
    # Method 2: Try google.genai.Client without parameters
    try:
        print("\n📍 Method 2: Trying google.genai.Client() without parameters...")
        from google.genai import Client
        client = Client()
        print("✅ Method 2 SUCCESS: google.genai.Client() without parameters")
        return client
    except Exception as e:
        print(f"❌ Method 2 FAILED: {e}")
    
    # Method 3: Try google.generativeai
    try:
        print("\n📍 Method 3: Trying google.generativeai...")
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        print("✅ Method 3 SUCCESS: google.generativeai.configure()")
        return genai
    except ImportError as e:
        print(f"❌ Method 3 FAILED: Import error - {e}")
    except Exception as e:
        print(f"❌ Method 3 FAILED: {e}")
    
    # Method 4: Try langchain_google_genai
    try:
        print("\n📍 Method 4: Trying langchain_google_genai...")
        from langchain_google_genai import ChatGoogleGenerativeAI
        client = ChatGoogleGenerativeAI(
            model="gemini-2.5-flash",
            api_key=api_key
        )
        print("✅ Method 4 SUCCESS: langchain_google_genai.ChatGoogleGenerativeAI")
        return client
    except ImportError as e:
        print(f"❌ Method 4 FAILED: Import error - {e}")
    except Exception as e:
        print(f"❌ Method 4 FAILED: {e}")
    
    print("\n❌ All methods failed!")
    return None


def test_simple_generation(client):
    """Test a simple content generation with the client."""
    if not client:
        print("❌ No client available for testing")
        return False
    
    try:
        print("\n🧪 Testing simple content generation...")
        
        # Test with different client types
        if hasattr(client, 'models') and hasattr(client.models, 'generate_content'):
            # google.genai.Client style
            response = client.models.generate_content(
                model="gemini-2.5-flash",
                contents="Say hello",
                config={"temperature": 0}
            )
            print("✅ Content generation SUCCESS with google.genai.Client style")
            return True
            
        elif hasattr(client, 'generate_content'):
            # google.generativeai style
            response = client.generate_content("Say hello")
            print("✅ Content generation SUCCESS with google.generativeai style")
            return True
            
        elif hasattr(client, 'invoke'):
            # LangChain style
            response = client.invoke("Say hello")
            print("✅ Content generation SUCCESS with LangChain style")
            return True
            
        else:
            print("❌ Unknown client type, cannot test generation")
            return False
            
    except Exception as e:
        print(f"❌ Content generation FAILED: {e}")
        return False


def main():
    """Main test function."""
    print("🔧 GOOGLE GENERATIVE AI CLIENT DIAGNOSTIC TOOL")
    print("=" * 60)
    
    # Test client initialization
    client = test_google_genai_client()
    
    # Test content generation
    if client:
        test_simple_generation(client)
    
    print("\n" + "=" * 60)
    print("📋 RECOMMENDATIONS:")
    
    if client:
        print("✅ A working client was found!")
        print("   Use the successful method in your code.")
    else:
        print("❌ No working client found. Try these solutions:")
        print("   1. Update google-genai: pip install --upgrade google-genai")
        print("   2. Install google-generativeai: pip install google-generativeai")
        print("   3. Check your GEMINI_API_KEY is valid")
        print("   4. Try using langchain_google_genai instead")


if __name__ == "__main__":
    main()
