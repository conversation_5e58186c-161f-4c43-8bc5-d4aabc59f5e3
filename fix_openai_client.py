#!/usr/bin/env python3
"""
Fix OpenAI client by trying different approaches and versions
"""

import os
import subprocess
import sys
from dotenv import load_dotenv

load_dotenv()

def check_environment():
    """Check the current environment and dependencies"""
    print("🔍 Checking current environment...")
    
    # Check Python version
    print(f"Python version: {sys.version}")
    
    # Check OpenAI API key
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        print(f"✅ OPENAI_API_KEY found: {api_key[:10]}...")
    else:
        print("❌ OPENAI_API_KEY not found")
        return False
    
    return True

def try_different_openai_versions():
    """Try different OpenAI library versions"""
    versions_to_try = [
        "1.54.4",  # Current version
        "1.35.0",  # Previous version
        "1.30.0",  # Older stable version
        "1.25.0",  # Even older
        "1.20.0",  # Much older
    ]
    
    for version in versions_to_try:
        print(f"\n📦 Trying OpenAI version {version}...")
        
        try:
            # Uninstall current version
            subprocess.run([sys.executable, "-m", "pip", "uninstall", "openai", "-y"], 
                         capture_output=True, check=False)
            
            # Install specific version
            result = subprocess.run([sys.executable, "-m", "pip", "install", f"openai=={version}"], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"❌ Failed to install OpenAI {version}")
                continue
            
            # Test the installation
            if test_openai_client():
                print(f"✅ SUCCESS! OpenAI {version} works!")
                return version
            else:
                print(f"❌ OpenAI {version} still has issues")
                
        except Exception as e:
            print(f"❌ Error with version {version}: {e}")
    
    return None

def test_openai_client():
    """Test if OpenAI client works"""
    try:
        # Clear any cached imports
        if 'openai' in sys.modules:
            del sys.modules['openai']
        
        from openai import OpenAI
        
        api_key = os.getenv("OPENAI_API_KEY")
        
        # Try basic initialization
        client = OpenAI(api_key=api_key)
        
        # Try a simple API call
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say 'test' in one word"}],
            max_tokens=5
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ OpenAI test successful! Response: '{result}'")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI test failed: {e}")
        return False

def try_alternative_installation():
    """Try alternative installation methods"""
    print("\n🔧 Trying alternative installation methods...")
    
    methods = [
        # Method 1: Clean install with no cache
        [sys.executable, "-m", "pip", "install", "--no-cache-dir", "--force-reinstall", "openai==1.54.4"],
        
        # Method 2: Install from source
        [sys.executable, "-m", "pip", "install", "--no-cache-dir", "git+https://github.com/openai/openai-python.git"],
        
        # Method 3: Install with specific dependencies
        [sys.executable, "-m", "pip", "install", "--no-cache-dir", "openai==1.54.4", "httpx==0.27.0", "pydantic==2.8.0"],
    ]
    
    for i, method in enumerate(methods, 1):
        print(f"\n📍 Method {i}: {' '.join(method)}")
        
        try:
            # Uninstall first
            subprocess.run([sys.executable, "-m", "pip", "uninstall", "openai", "-y"], 
                         capture_output=True, check=False)
            
            # Try installation method
            result = subprocess.run(method, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Installation method {i} succeeded")
                if test_openai_client():
                    print(f"🎉 Method {i} works! OpenAI client is functional!")
                    return True
                else:
                    print(f"❌ Method {i} installed but client still fails")
            else:
                print(f"❌ Installation method {i} failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Method {i} error: {e}")
    
    return False

def check_conflicts():
    """Check for potential package conflicts"""
    print("\n🔍 Checking for potential conflicts...")
    
    # Check for conflicting packages
    conflicting_packages = [
        "openai-python",
        "openai-api",
        "openai-client",
        "python-openai"
    ]
    
    for package in conflicting_packages:
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "show", package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"⚠️ Found potentially conflicting package: {package}")
                print(f"   Uninstalling {package}...")
                subprocess.run([sys.executable, "-m", "pip", "uninstall", package, "-y"], 
                             capture_output=True)
        except:
            pass
    
    # Check for proxy-related environment variables
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    for var in proxy_vars:
        if os.getenv(var):
            print(f"⚠️ Found proxy environment variable: {var}={os.getenv(var)}")

def main():
    """Main function to fix OpenAI client"""
    print("🔧 OPENAI CLIENT REPAIR TOOL")
    print("=" * 60)
    
    if not check_environment():
        print("❌ Environment check failed")
        return
    
    # Check for conflicts first
    check_conflicts()
    
    # Try current installation
    print("\n📍 Testing current OpenAI installation...")
    if test_openai_client():
        print("✅ OpenAI client already works!")
        return
    
    # Try different versions
    working_version = try_different_openai_versions()
    if working_version:
        print(f"\n🎉 SUCCESS! OpenAI version {working_version} is working!")
        return
    
    # Try alternative installation methods
    if try_alternative_installation():
        print("\n🎉 SUCCESS! Alternative installation method worked!")
        return
    
    print("\n❌ All attempts failed. The issue might be:")
    print("   1. Network/firewall blocking OpenAI API")
    print("   2. Invalid API key")
    print("   3. System-level Python/pip issues")
    print("   4. Conflicting system packages")
    print("\n💡 Try:")
    print("   - Check your internet connection")
    print("   - Verify your OpenAI API key at https://platform.openai.com/api-keys")
    print("   - Try in a fresh virtual environment")

if __name__ == "__main__":
    main()
