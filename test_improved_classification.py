#!/usr/bin/env python3
"""
Test script for the improved PDF classification system.

This script demonstrates the new classification logic:
1. Keep standalone reports (delete consolidated)
2. If consolidated available → delete it
3. If both standalone + consolidated in single PDF → keep it
"""

import sys
import os

# Add src directory to path
sys.path.append('src')

from improved_pdf_classifier import classify_pdfs_for_plant

def main():
    """Test the improved classification system."""
    print("🧪 TESTING IMPROVED PDF CLASSIFICATION SYSTEM")
    print("=" * 60)
    
    # Test with SEIL Energy India Limited
    plant_name = "SEIL Energy India Limited"
    
    print(f"🏭 Testing classification for: {plant_name}")
    print("\n📝 NEW CLASSIFICATION RULES:")
    print("   ✅ KEEP: Standalone reports")
    print("   ✅ KEEP: PDFs with both standalone + consolidated")
    print("   🗑️ DELETE: Consolidated-only reports")
    print("\n" + "=" * 60)
    
    try:
        classify_pdfs_for_plant(plant_name)
    except Exception as e:
        print(f"❌ Error during classification: {e}")
        print("\nℹ️  Make sure you have:")
        print("   1. Downloaded PDFs in downloads/SEIL_Energy_India_Limited/")
        print("   2. Set OPENAI_API_KEY in your .env file")
        print("   3. Installed required packages (langchain, openai, etc.)")

if __name__ == "__main__":
    main()
