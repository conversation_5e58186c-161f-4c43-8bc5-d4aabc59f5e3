# LangChain ecosystem - compatible versions
langchain
langchain-core
langchain-community
langgraph

# Core dependencies for PDF processing and document analysis
pdf2image
unstructured[pdf]

# OpenAI API client
openai

# Environment variables
python-dotenv

# Additional dependencies that might be needed by unstructured
pillow
pdfminer.six
pypdf

# System dependencies (install via system package manager)
# For Ubuntu/Debian: sudo apt-get install poppler-utils
# For macOS: brew install poppler
# For Windows: Download poppler binaries and add to PATH

# Optional: For better OCR support
# pytesseract==0.3.10
# tesseract (system dependency)