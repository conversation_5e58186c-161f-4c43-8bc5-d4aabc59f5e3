#!/usr/bin/env python3
"""
Minimal OpenAI test to isolate the issue
"""

import os
import sys

# Set up environment
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "")

print("🔍 Minimal OpenAI Test")
print("=" * 40)

print(f"Python version: {sys.version}")
print(f"API key set: {bool(os.getenv('OPENAI_API_KEY'))}")

try:
    print("\n1. Testing import...")
    import openai
    print(f"✅ OpenAI version: {openai.__version__}")
    
    print("\n2. Testing OpenAI class import...")
    from openai import OpenAI
    print("✅ OpenAI class imported successfully")
    
    print("\n3. Testing client creation with minimal parameters...")
    # Try with just the API key
    client = OpenAI()  # Should use OPENAI_API_KEY env var
    print("✅ Client created successfully!")
    
    print("\n4. Testing a simple API call...")
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[{"role": "user", "content": "Hello"}],
        max_tokens=5
    )
    print(f"✅ API call successful: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"\n❌ Error occurred: {e}")
    print(f"Error type: {type(e).__name__}")
    
    # Print detailed traceback
    import traceback
    print("\nFull traceback:")
    traceback.print_exc()
    
    # Try to get more details about the error
    if "proxies" in str(e):
        print("\n🔍 This is the 'proxies' error we're trying to fix!")
        print("Let's try some workarounds...")
        
        try:
            print("\nTrying workaround 1: Import with different method...")
            import importlib
            openai_module = importlib.import_module('openai')
            OpenAI_class = getattr(openai_module, 'OpenAI')
            
            print("Trying to create client with no parameters...")
            client = OpenAI_class()
            print("✅ Workaround 1 successful!")
            
        except Exception as e2:
            print(f"❌ Workaround 1 failed: {e2}")
            
            try:
                print("\nTrying workaround 2: Manual configuration...")
                # Try setting the API key after creation
                client = OpenAI_class()
                client.api_key = os.getenv("OPENAI_API_KEY")
                print("✅ Workaround 2 successful!")
                
            except Exception as e3:
                print(f"❌ Workaround 2 failed: {e3}")
                
                print("\n💡 Suggestions:")
                print("1. Try reinstalling openai: pip uninstall openai && pip install openai")
                print("2. Check for conflicting packages")
                print("3. Try in a fresh virtual environment")
                print("4. Check if there are any proxy settings interfering")

print("\n" + "=" * 40)
print("Test complete!")
