#!/usr/bin/env python3
"""
Test the comprehensive scraper fix
"""

import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_comprehensive_scraper():
    """Test if the comprehensive scraper can be imported and initialized"""
    print("🔧 Testing Comprehensive Scraper Fix")
    print("=" * 50)
    
    try:
        print("1. Testing import...")
        from agent.comprehensive_automated_scraper import ComprehensiveAutomatedScraper
        print("✅ Import successful")
        
        print("2. Testing initialization...")
        scraper = ComprehensiveAutomatedScraper(download_dir="./test_downloads")
        print("✅ Initialization successful")
        
        print("3. Testing stats attribute...")
        print(f"Stats: {scraper.stats}")
        print("✅ Stats attribute exists")
        
        print("4. Testing OpenAI client...")
        print(f"OpenAI client: {scraper.openai_client is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_comprehensive_scraper()
    
    if success:
        print("\n✅ Comprehensive scraper is working!")
    else:
        print("\n❌ Comprehensive scraper has issues")
