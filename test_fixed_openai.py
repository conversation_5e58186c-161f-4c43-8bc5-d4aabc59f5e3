#!/usr/bin/env python3
"""
Test the fixed OpenAI client with proper import order
"""

import os
from dotenv import load_dotenv

# Load environment variables FIRST
load_dotenv()

# Import OpenAI BEFORE any other modules that might interfere
from openai import OpenAI

def test_openai_client():
    """Test OpenAI client with the fixed import order"""
    print("🔧 Testing OpenAI client with fixed import order...")
    
    try:
        # Test basic client creation
        print("1. Creating OpenAI client...")
        client = OpenAI()
        print("✅ Client created successfully!")
        
        # Test API call
        print("2. Testing API call...")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say hello in one word"}],
            max_tokens=5
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ API call successful! Response: '{result}'")
        
        return client
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if "proxies" in str(e):
            print("⚠️ Still getting the proxies error!")
        return None

def test_with_application_imports():
    """Test OpenAI after importing application modules"""
    print("\n🔧 Testing with application imports...")
    
    try:
        # Import modules in the same order as our application
        print("1. Importing application modules...")
        
        import sys
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        # Import the modules that might cause issues
        from agent.graph import power_plant_graph
        from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper
        from agent.content_analyzer import ContentAnalyzer
        
        print("✅ Application modules imported")
        
        # Test OpenAI client
        print("2. Testing OpenAI client after imports...")
        client = OpenAI()
        
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Test"}],
            max_tokens=3
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ OpenAI works after application imports! Response: '{result}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error after application imports: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 FIXED OPENAI CLIENT TEST")
    print("=" * 50)
    
    # Test 1: Basic OpenAI client
    client = test_openai_client()
    
    if client:
        print("✅ Basic OpenAI client works!")
        
        # Test 2: With application imports
        if test_with_application_imports():
            print("\n🎉 SUCCESS! OpenAI client works with application imports!")
            print("   The import order fix resolved the issue!")
        else:
            print("\n❌ OpenAI client fails with application imports")
            print("   Need to investigate further...")
    else:
        print("\n❌ Basic OpenAI client doesn't work")
        print("   The issue is not resolved yet...")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
