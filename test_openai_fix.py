#!/usr/bin/env python3
"""
Test the improved OpenAI client initialization
"""

import os
from dotenv import load_dotenv

load_dotenv()

def test_openai_initialization():
    """Test the improved OpenAI client initialization"""
    print("🔧 Testing improved OpenAI client initialization...")
    
    from openai import OpenAI
    
    api_key = os.getenv("OPENAI_API_KEY")
    
    if not api_key:
        print("❌ OPENAI_API_KEY not found in environment variables")
        return None
    
    print(f"✅ Found OPENAI_API_KEY: {api_key[:10]}...")
    
    # Strategy 1: Standard initialization
    try:
        print("\n📍 Strategy 1: Standard initialization...")
        client = OpenAI(api_key=api_key)
        # Test the client with a simple call
        models = client.models.list()
        print(f"✅ Standard initialization successful! Found {len(models.data)} models")
        return client
    except Exception as e:
        print(f"❌ Standard initialization failed: {e}")
    
    # Strategy 2: Minimal initialization
    try:
        print("\n📍 Strategy 2: Minimal initialization...")
        client = OpenAI()
        client.api_key = api_key
        # Test the client
        models = client.models.list()
        print(f"✅ Minimal initialization successful! Found {len(models.data)} models")
        return client
    except Exception as e:
        print(f"❌ Minimal initialization failed: {e}")
    
    # Strategy 3: Environment variable approach
    try:
        print("\n📍 Strategy 3: Environment variable approach...")
        os.environ["OPENAI_API_KEY"] = api_key
        client = OpenAI()
        # Test the client
        models = client.models.list()
        print(f"✅ Environment variable initialization successful! Found {len(models.data)} models")
        return client
    except Exception as e:
        print(f"❌ Environment variable initialization failed: {e}")
    
    print("\n❌ All OpenAI initialization strategies failed.")
    return None


def test_simple_completion(client):
    """Test a simple completion with the client"""
    if not client:
        print("❌ No client available for testing")
        return False
    
    try:
        print("\n🧪 Testing simple completion...")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "user", "content": "Say hello in one word"}
            ],
            max_tokens=10
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ Completion successful! Response: '{result}'")
        return True
        
    except Exception as e:
        print(f"❌ Completion failed: {e}")
        return False


def main():
    """Main test function"""
    print("🔧 OPENAI CLIENT DIAGNOSTIC TOOL")
    print("=" * 60)
    
    # Test client initialization
    client = test_openai_initialization()
    
    # Test completion
    if client:
        test_simple_completion(client)
    
    print("\n" + "=" * 60)
    if client:
        print("✅ OpenAI client is working correctly!")
        print("   The classification should now work properly.")
    else:
        print("❌ OpenAI client issues persist.")
        print("   Check your OPENAI_API_KEY and internet connection.")


if __name__ == "__main__":
    main()
