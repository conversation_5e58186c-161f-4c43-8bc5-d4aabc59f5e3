#!/usr/bin/env python3
"""
Integrated Power Plant Annual Report Scraper with PDF Validation

This script provides a fully automated solution that:
1. Searches for power plant annual reports
2. Scrapes PDFs from identified sources
3. Validates and classifies PDFs (checks if they are consolidated reports)
4. Removes invalid PDFs automatically
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, List
import requests
import re
from langchain_core.messages import HumanMessage

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from agent.graph import power_plant_graph
from agent.configuration import Configuration
from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper
from agent.content_analyzer import ContentAnalyzer

# Load environment variables FIRST
from dotenv import load_dotenv
load_dotenv()

# Import OpenAI BEFORE any other modules that might interfere
from openai import OpenAI


class IntegratedPowerPlantScraper:
    """Integrated scraper that combines PDF downloading with validation and classification."""
    
    def __init__(self, download_dir: str = "./downloads"):
        """Initialize the integrated scraper.
        
        Args:
            download_dir: Directory to save downloaded PDFs
        """
        self.download_dir = download_dir
        self.scraper_api = ScraperAPIPDFScraper(download_dir=download_dir)
        self.content_analyzer = ContentAnalyzer()
        
        # Initialize OpenAI client for PDF validation
        try:
            self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        except TypeError as e:
            if "proxies" in str(e) or "unexpected keyword argument" in str(e):
                # Handle OpenAI client version compatibility issue
                print("⚠️ OpenAI client version compatibility issue detected")
                try:
                    # Try alternative initialization
                    self.openai_client = OpenAI()
                    self.openai_client.api_key = os.getenv("OPENAI_API_KEY")
                except Exception:
                    print("⚠️ Warning: Could not initialize OpenAI client. PDF classification will be limited.")
                    self.openai_client = None
            else:
                raise e
        
        # Create download directory
        os.makedirs(download_dir, exist_ok=True)
    
    def initialize_power_plant_state(self, plant_name: str) -> Dict[str, Any]:
        """Initialize the state for power plant annual report search."""
        return {
            "messages": [HumanMessage(content=plant_name)],
            "plant_name": plant_name,
            "search_phase": "direct_search",
            "search_query": [],
            "web_research_result": [],
            "sources_gathered": [],
            "annual_report_urls": [],
            "initial_search_query_count": 2,
            "max_research_loops": 2,
            "research_loop_count": 0,
            "reasoning_model": "gemini-2.5-flash",
            "holding_company_name": "",
            "found_annual_reports": False,
        }
    
    def get_pdf_file_paths(self, plant_name: str, base_dir: str = None) -> List[str]:
        """Get all PDF file paths for a specific plant.
        
        Args:
            plant_name: Name of the power plant
            base_dir: Base directory (uses self.download_dir if not provided)
            
        Returns:
            List of PDF file paths
        """
        if base_dir is None:
            base_dir = self.download_dir
            
        folder_name = plant_name.replace(" ", "_").replace('"', '')
        plant_dir = Path(base_dir) / folder_name
     
        if not os.path.isdir(plant_dir):
            print(f"⚠️ Directory not found: {plant_dir}")
            return []
     
        pdf_paths = [str(file_path) for file_path in plant_dir.glob("*.pdf")]
        return pdf_paths
    
    def extract_first_5_pages_text_langchain(self, pdf_path: str, max_pages: int = 5) -> str:
        """Extract text from the first N pages of a PDF using available methods.

        Args:
            pdf_path: Path to the PDF file
            max_pages: Maximum number of pages to extract

        Returns:
            Extracted text content
        """
        # Try multiple PDF extraction methods

        # Method 1: Try PyPDF2 if available
        try:
            import PyPDF2
            text = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                num_pages = min(len(pdf_reader.pages), max_pages)

                for page_num in range(num_pages):
                    page = pdf_reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"

            if text.strip():
                return text.strip()
        except ImportError:
            print("   ⚠️ PyPDF2 not available, trying alternative methods...")
        except Exception as e:
            print(f"   ⚠️ PyPDF2 extraction failed: {e}")

        # Method 2: Try langchain if available
        try:
            from langchain.document_loaders import UnstructuredPDFLoader
            loader = UnstructuredPDFLoader(
                file_path=pdf_path,
                strategy="auto",
                languages=["eng"]
            )
            documents = loader.load()

            text = ""
            for i, doc in enumerate(documents):
                if i >= max_pages:
                    break
                text += doc.page_content + "\n"

            if text.strip():
                return text.strip()
        except ImportError:
            print("   ⚠️ LangChain UnstructuredPDFLoader not available")
        except Exception as e:
            print(f"   ⚠️ LangChain extraction failed: {e}")

        # Method 3: Basic file reading (fallback)
        try:
            # Just read the file as text (won't work well for PDFs but better than nothing)
            with open(pdf_path, 'rb') as file:
                # Read first few KB and try to decode
                content = file.read(10000)  # First 10KB
                try:
                    text = content.decode('utf-8', errors='ignore')
                    if text.strip():
                        return text.strip()
                except:
                    pass
        except Exception as e:
            print(f"   ❌ All PDF extraction methods failed: {e}")

        print(f"   ❌ Could not extract text from {os.path.basename(pdf_path)}")
        return ""
    
    def is_valid_annual_report(self, text: str, plant_name: str, model: str = "gpt-4o") -> bool:
        """Check if a PDF is a valid annual report with consolidated financials.
        
        Args:
            text: Extracted text from PDF
            plant_name: Name of the power plant
            model: OpenAI model to use
            
        Returns:
            True if the PDF is a valid annual report with consolidated financials
        """
        system_prompt = (
            "You are a document classification assistant. Your task is to check whether a given document "
            "is an annual report and whether it belongs to the specified power plant. Additionally, determine "
            "whether both standalone and consolidated reports are included."
        )
     
        user_prompt = f"""
        You are given the beginning of a document. Please answer YES or NO only for each question.
     
        1. Is this document an *annual report*?
        2. Does this annual report belong to the power plant named "{plant_name}"?
        3. Does the document include both *standalone* and *consolidated* financial reports?
     
        Respond strictly in this format:
        Annual report: YES or NO along with the reason  
        Belongs to plant: YES or NO along with the reason  
        Both standalone and consolidated: YES or NO along with the reason
     
        Here is the text:
        {text[:100000]}
     
        NOTE: The power plant name may be abbreviated or phrased differently. Use judgment in matching it.
        """
     
        try:
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0
            )
     
            content = response.choices[0].message.content
            print("🔍 GPT-4o Reasoning:\n" + content.strip() + "\n")
     
            # Convert to lowercase for consistent checks
            content_lower = content.lower()
     
            # Extract flags
            is_annual = "annual report: yes" in content_lower
            is_correct_plant = "belongs to plant: yes" in content_lower
            has_both_reports = "both standalone and consolidated: yes" in content_lower
     
            return is_annual and is_correct_plant and has_both_reports
     
        except Exception as e:
            print(f"❌ Error verifying report: {e}")
            return False
    
    def delete_if_invalid(self, flag: bool, pdf_path: str) -> bool:
        """Delete PDF if it's invalid.
        
        Args:
            flag: True if PDF is valid, False if invalid
            pdf_path: Path to the PDF file
            
        Returns:
            True if PDF was kept, False if deleted
        """
        if flag:
            print(f"✅ Keeping: {os.path.basename(pdf_path)}")
            return True
        else:
            try:
                os.remove(pdf_path)
                print(f"🗑️ Deleted: {os.path.basename(pdf_path)}")
                return False
            except Exception as e:
                print(f"❌ Failed to delete {pdf_path}: {e}")
                return False
    
    def process_pdf_list_with_validation(self, pdf_list: List[str], plant_name: str, 
                                       model: str = "gpt-4o", max_pages: int = 5) -> List[str]:
        """Process and validate a list of PDFs, keeping only valid consolidated annual reports.
        
        Args:
            pdf_list: List of PDF file paths
            plant_name: Name of the power plant
            model: OpenAI model to use for validation
            max_pages: Maximum pages to extract for validation
            
        Returns:
            List of valid PDF paths that were kept
        """
        print(f"\n📋 Validating {len(pdf_list)} downloaded PDFs...")
        valid_pdfs = []
        
        for each_pdf in pdf_list:
            print(f"\n🔍 Processing: {os.path.basename(each_pdf)}")
            text = self.extract_first_5_pages_text_langchain(each_pdf, max_pages=max_pages)
            
            if not text:
                print(f"❌ Could not extract text from {os.path.basename(each_pdf)}")
                self.delete_if_invalid(False, each_pdf)
                continue
                
            validation_flag = self.is_valid_annual_report(text, plant_name, model=model)
            print(f"📊 Validation result for {os.path.basename(each_pdf)}: {validation_flag}")
            
            if self.delete_if_invalid(validation_flag, each_pdf):
                valid_pdfs.append(each_pdf)
        
        return valid_pdfs

    def extract_urls_from_ai_response(self, final_state: Dict[str, Any]) -> List[str]:
        """Extract URLs from AI response for more accurate scraping.

        Args:
            final_state: Final state from the graph execution

        Returns:
            List of URLs found in AI response
        """
        # Use the same URL resolution logic as power_plant_search.py

        answer_urls = []
        if final_state and final_state.get("messages"):
            final_message = final_state["messages"][-1]
            if hasattr(final_message, 'content'):
                answer = final_message.content

                # Extract URLs from the answer using regex
                found_urls = re.findall(r'https?://[^\s\)\]\*]+', answer)

                # Clean up URLs (remove trailing punctuation and markdown)
                clean_urls = []
                for url in found_urls:
                    # Remove trailing punctuation and markdown
                    url = url.rstrip('.,;:!?)*')
                    # Remove markdown formatting
                    url = url.replace('**', '')
                    # Remove trailing slash if it's not part of the domain
                    if url.endswith('/') and url.count('/') > 2:
                        url = url.rstrip('/')

                    # Filter out invalid URLs (same logic as power_plant_search.py)
                    if (url.startswith('http') and
                        not '/id/' in url):
                        clean_urls.append(url)
                        print(f"   🎯 AI-identified URL: {url}")

                # Resolve redirect URLs
                if clean_urls:
                    print(f"   🔍 Resolving {len(clean_urls)} redirect URLs...")
                    resolved_urls = []

                    for url in clean_urls:
                        if 'grounding-api-redirect' in url or 'vertexaisearch.cloud.google.com' in url:
                            try:
                                response = requests.head(url, allow_redirects=True, timeout=10)
                                resolved_url = response.url
                                print(f"      ✅ Resolved: {url[:50]}... → {resolved_url[:50]}...")
                                resolved_urls.append(resolved_url)
                            except Exception as e:
                                print(f"      ❌ Failed to resolve: {url[:50]}... ({str(e)})")
                        else:
                            resolved_urls.append(url)
                            print(f"      ✅ Direct URL (no redirect): {url[:50]}...")

                    answer_urls = resolved_urls

                # If we found AI URLs, prioritize them over redirect URLs
                if answer_urls:
                    print(f"   ✅ Found {len(answer_urls)} AI-identified URLs - these are more reliable!")
                else:
                    print(f"   ⚠️  No direct URLs found in AI answer, will use redirect URLs")

        return answer_urls



    def scrape_pdfs_from_sources(self, sources: List[Dict[str, Any]], plant_name: str,
                                final_state: Dict[str, Any] = None) -> List[str]:
        """Scrape PDF annual reports from identified sources using the proven logic from power_plant_search.py.

        Args:
            sources: List of source dictionaries with URLs
            plant_name: Name of the power plant
            final_state: Final state containing the AI's answer with URLs

        Returns:
            List of downloaded PDF file paths
        """
        print("🤖 Starting automated PDF scraping process...")

        # Import the exact same scraping function from power_plant_search.py
        from power_plant_search import scrape_annual_report_pdfs

        try:
            # Use the exact same scraping logic as power_plant_search.py
            # This function handles all the URL resolution, content analysis, and PDF downloading
            scrape_annual_report_pdfs(sources, plant_name, final_state)

            # Get the downloaded files from the plant directory
            downloaded_files = self.get_pdf_file_paths(plant_name, self.download_dir)

            return downloaded_files

        except Exception as e:
            print(f"❌ Error during PDF scraping: {str(e)}")
            return []

    def try_direct_search_fallback(self, plant_name: str) -> List[str]:
        """Try direct search as fallback when main search fails.

        Args:
            plant_name: Name of the power plant

        Returns:
            List of downloaded PDF file paths
        """
        print("\n🔄 Trying direct search fallback...")
        downloaded_files = []

        # Try direct search for the last 5 years
        current_year = 2024
        from_year = current_year - 4
        to_year = current_year

        # Generic search for all power plants (no hardcoding)
        print(f"🔍 Using generic search for: {plant_name}")

        # Try direct search first
        plant_files = self.scraper_api.run_downloader(plant_name, from_year, to_year)
        if plant_files:
            downloaded_files.extend(plant_files)
            print(f"✅ Downloaded {len(plant_files)} PDF(s) from direct search")

        # Try direct search with plant name
        print(f"🔍 Searching directly for {plant_name} annual reports...")
        plant_files = self.scraper_api.run_downloader(plant_name, from_year, to_year)
        if plant_files:
            downloaded_files.extend(plant_files)
            print(f"✅ Downloaded {len(plant_files)} PDF(s) for {plant_name}")

        return downloaded_files

    def run_integrated_search_and_validation(self, plant_name: str) -> Dict[str, Any]:
        """Run the complete integrated search, scraping, and validation process.

        Args:
            plant_name: Name of the power plant to search for

        Returns:
            Dictionary with results including valid PDFs found
        """
        print(f"🏭 Starting integrated search for: {plant_name}")
        print("="*60)

        results = {
            "plant_name": plant_name,
            "search_successful": False,
            "pdfs_downloaded": [],
            "valid_pdfs": [],
            "total_downloaded": 0,
            "total_valid": 0,
            "errors": []
        }

        try:
            # Step 1: Run AI-powered search
            print("🔍 Step 1: Running AI-powered search...")
            initial_state = self.initialize_power_plant_state(plant_name)

            try:
                final_state = power_plant_graph.invoke(initial_state)

                if final_state and final_state.get("messages") and final_state.get("sources_gathered"):
                    print("✅ AI search completed successfully")
                    results["search_successful"] = True

                    # Check if we have valid sources (not just Google redirect URLs)
                    sources = final_state.get("sources_gathered", [])
                    valid_sources = []
                    for source in sources:
                        url = source.get('value', '')
                        if (url.startswith('http') and
                            not 'vertexaisearch.cloud.google.com' in url and
                            not '/id/' in url and
                            not 'grounding-api-redirect' in url):
                            valid_sources.append(source)

                    if valid_sources:
                        # Step 2: Scrape PDFs from identified sources
                        print(f"\n🔍 Step 2: Scraping PDFs from {len(valid_sources)} valid sources...")
                        downloaded_files = self.scrape_pdfs_from_sources(
                            valid_sources,
                            plant_name,
                            final_state
                        )
                    else:
                        print("⚠️ AI search found only invalid redirect URLs, trying direct search...")
                        downloaded_files = self.try_direct_search_fallback(plant_name)

                else:
                    print("⚠️ AI search returned incomplete results, trying direct search...")
                    downloaded_files = self.try_direct_search_fallback(plant_name)

            except Exception as e:
                print(f"❌ AI search failed: {str(e)}")
                results["errors"].append(f"AI search failed: {str(e)}")
                downloaded_files = self.try_direct_search_fallback(plant_name)

            # Step 3: If no PDFs found, try direct search
            if not downloaded_files:
                print("\n🔄 No PDFs found from main search, trying direct search...")
                downloaded_files = self.try_direct_search_fallback(plant_name)

            # Update results with downloaded files
            results["pdfs_downloaded"] = downloaded_files
            results["total_downloaded"] = len(downloaded_files)

            if downloaded_files:
                print(f"\n✅ Downloaded {len(downloaded_files)} PDF(s) total")

                # Step 4: Validate and classify PDFs
                print("\n🔍 Step 3: Validating and classifying PDFs...")
                valid_pdfs = self.process_pdf_list_with_validation(downloaded_files, plant_name)

                results["valid_pdfs"] = valid_pdfs
                results["total_valid"] = len(valid_pdfs)

                print(f"\n📊 Final Results:")
                print(f"   📥 Total PDFs downloaded: {len(downloaded_files)}")
                print(f"   ✅ Valid consolidated annual reports: {len(valid_pdfs)}")
                print(f"   🗑️ Invalid PDFs removed: {len(downloaded_files) - len(valid_pdfs)}")

                if valid_pdfs:
                    print(f"\n📁 Valid PDFs saved in: {self.download_dir}/{plant_name.replace(' ', '_')}/")
                    for pdf_path in valid_pdfs:
                        print(f"   📄 {os.path.basename(pdf_path)}")
                else:
                    print("\n❌ No valid consolidated annual reports found")

            else:
                print("\n❌ No PDF files were downloaded")
                results["errors"].append("No PDF files were downloaded")

        except Exception as e:
            error_msg = f"Unexpected error during integrated search: {str(e)}"
            print(f"❌ {error_msg}")
            results["errors"].append(error_msg)

        return results


def main():
    """Main function to run the integrated power plant scraper."""
    print("🏭 Integrated Power Plant Annual Report Scraper")
    print("=" * 50)
    print("This tool automatically:")
    print("1. Searches for power plant annual reports")
    print("2. Downloads PDFs from identified sources")
    print("3. Validates and classifies PDFs")
    print("4. Keeps only Standalone annual reports")
    print()

    # Check for required environment variables
    required_keys = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
    missing_keys = [key for key in required_keys if not os.getenv(key)]

    if missing_keys:
        print("❌ Error: Missing required environment variables:")
        for key in missing_keys:
            print(f"   - {key}")
        print("\nPlease set these environment variables before running this script.")
        sys.exit(1)

    # Warn about optional keys
    if not os.getenv("SCRAPER_API_KEY"):
        print("⚠️  Warning: SCRAPER_API_KEY not set. PDF scraping will be limited.")
        print()

    while True:
        try:
            # Get power plant name from user
            plant_name = input("Enter power plant name (or 'quit' to exit): ").strip()

            if plant_name.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break

            if not plant_name:
                print("❌ Please enter a valid power plant name.")
                continue

            # Initialize the integrated scraper
            scraper = IntegratedPowerPlantScraper(download_dir="./downloads")

            # Run the integrated search and validation
            scraper.run_integrated_search_and_validation(plant_name)

            # Ask if user wants to search for another plant
            print("\n" + "-"*50)
            continue_search = input("Search for another power plant? (y/n): ").strip().lower()
            if continue_search not in ['y', 'yes']:
                print("👋 Goodbye!")
                break

        except KeyboardInterrupt:
            print("\n\n👋 Search interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
            continue


if __name__ == "__main__":
    main()
