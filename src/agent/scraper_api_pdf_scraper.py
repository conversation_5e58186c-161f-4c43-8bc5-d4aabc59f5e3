"""
ScraperAPI PDF Scraper Module for Power Plant Annual Reports

This module provides functionality to scrape PDF annual reports using ScraperAPI
instead of Selenium, which can be more reliable for certain websites.
"""

import os
import time
import requests
import urllib3
import urllib.parse
import asyncio
from pathlib import Path
from typing import List, Dict, Optional, Any
from bs4 import BeautifulSoup
from dotenv import load_dotenv

# Load API key
load_dotenv()
SCRAPER_API_KEY = os.getenv("SCRAPER_API_KEY")
SCRAPER_API_URL = "https://api.scraperapi.com/structured/google/search"
HEADERS = {"User-Agent": "Mozilla/5.0"}

# Disable insecure request warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class ScraperAPIPDFScraper:
    """Class to handle PDF scraping from annual report pages using ScraperAPI."""
    
    def __init__(self, download_dir: str = "./annual_reports"):
        """Initialize the PDF scraper.

        Args:
            download_dir: Directory to save downloaded PDFs
        """
        self.download_dir = os.path.abspath(download_dir)

        # Create download directory if it doesn't exist
        os.makedirs(self.download_dir, exist_ok=True)

        # Adaptive method selection
        self.preferred_method = "scraperapi"  # scraperapi, direct, or playwright
        self.method_failures = {"scraperapi": 0, "direct": 0}

    def _generate_unique_filename(self, url: str, plant_name: str, year: str = None) -> str:
        """Generate unique filename for PDF from URL"""
        import re
        import hashlib

        # Try to extract meaningful filename from URL
        if 'path=' in url:
            # Extract filename from path parameter (like PLTU Suparma)
            path_match = re.search(r'path=([^&]+)', url)
            if path_match:
                filename = path_match.group(1)
                if not filename.endswith('.pdf'):
                    filename += '.pdf'
            else:
                filename = f"{plant_name.replace(' ', '_')}_{year or 'report'}.pdf"
        elif url.lower().endswith('.pdf'):
            # Extract filename from URL path
            filename = os.path.basename(url.split('?')[0])
            if not filename or filename == '.pdf':
                filename = f"{plant_name.replace(' ', '_')}_{year or 'report'}.pdf"
        else:
            # Generate filename with URL hash for uniqueness
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            filename = f"{plant_name.replace(' ', '_')}_{year or 'report'}_{url_hash}.pdf"

        # Clean filename - remove problematic characters
        filename = re.sub(r'[<>:"/\\|?*%]', '_', filename)
        filename = filename.replace(' ', '_')

        # Ensure .pdf extension
        if not filename.lower().endswith('.pdf'):
            filename += '.pdf'

        return filename
    
    def search_pdf_url(self, query: str) -> Optional[str]:
        """Search for a PDF URL using ScraperAPI.
        
        Args:
            query: Search query string
            
        Returns:
            PDF URL if found, None otherwise
        """
        if not SCRAPER_API_KEY:
            print("❌ Error: SCRAPER_API_KEY environment variable is not set.")
            return None
            
        params = {
            "api_key": SCRAPER_API_KEY,
            "query": query,
            "num": 10  # Increased to get more results
        }
        
        try:
            print(f"🔍 Searching for PDF: {query}")
            
            # Add timeout and retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = requests.get(
                        SCRAPER_API_URL, 
                        params=params, 
                        headers=HEADERS,
                        timeout=30  # 30 second timeout
                    )
                    response.raise_for_status()
                    break
                except (requests.exceptions.RequestException, requests.exceptions.Timeout) as e:
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # Exponential backoff
                        print(f"⚠️ Request failed (attempt {attempt+1}/{max_retries}): {e}")
                        print(f"   Retrying in {wait_time} seconds...")
                        time.sleep(wait_time)
                    else:
                        print(f"❌ All {max_retries} attempts failed: {e}")
                        return None
            
            # Parse results
            try:
                results = response.json().get("organic_results", [])
            except ValueError:
                print("❌ Failed to parse JSON response")
                print(f"Response: {response.text[:200]}...")  # Print first 200 chars of response
                return None
                
            if not results:
                print("⚠️ No search results found")
                return None
                
            print(f"   Found {len(results)} search results")
            
            # Get the first valid PDF URL
            pdf_links = []
            for result in results:
                link = result.get("link", "")
                title = result.get("title", "")
                
                if link.lower().endswith(".pdf"):
                    pdf_links.append((link, title))
            
            if pdf_links:
                # Choose the first PDF link
                chosen_link, title = pdf_links[0]
                print(f"✅ Found PDF: {chosen_link}")
                print(f"   Title: {title}")
                return chosen_link
                    
            print("⚠️ No direct PDF links found in search results")
            return None
            
        except Exception as e:
            print(f"❌ Error during PDF search: {e}")
            return None
    
    def download_pdf_smart(self, url: str, plant_name: str, year: Optional[str] = None) -> Optional[str]:
        """Smart PDF download that adapts method based on previous failures"""

        # Determine which method to try first
        if self.preferred_method == "scraperapi" and SCRAPER_API_KEY:
            return self._try_scraperapi_download(url, plant_name, year)
        elif self.preferred_method == "direct":
            return self._try_direct_download(url, plant_name, year)
        else:
            # Fallback to original method
            return self.download_pdf(url, plant_name, year)

    def _try_scraperapi_download(self, url: str, plant_name: str, year: Optional[str] = None) -> Optional[str]:
        """Try ScraperAPI download with single attempt"""
        try:
            # Create plant-specific directory
            plant_dir = os.path.join(self.download_dir, plant_name.replace(' ', '_'))
            os.makedirs(plant_dir, exist_ok=True)

            # Generate unique filename
            filename = self._generate_unique_filename(url, plant_name, year)
            file_path = os.path.join(plant_dir, filename)

            # Check if file already exists
            if os.path.exists(file_path):
                print(f"⏭️  Skipping (already exists): {filename}")
                return file_path

            print(f"⬇️  Downloading: {url} to {file_path}")

            # Single ScraperAPI attempt
            encoded_url = urllib.parse.quote(url, safe=':/?#[]@!$&\'()*+,;=')
            scraper_url = f"http://api.scraperapi.com?api_key={SCRAPER_API_KEY}&url={encoded_url}"
            r = requests.get(scraper_url, stream=True, timeout=30, verify=False)
            r.raise_for_status()

            # Download successful, continue with ScraperAPI
            return self._save_pdf_content(r, file_path, filename)

        except Exception as e:
            print(f"⚠️ ScraperAPI failed: {e}")
            self.method_failures["scraperapi"] += 1

            # Switch to direct method for future downloads
            if self.method_failures["scraperapi"] >= 1:
                print("🔄 Switching to direct downloads for remaining PDFs...")
                self.preferred_method = "direct"

            # Try direct download as immediate fallback
            return self._try_direct_download(url, plant_name, year)

    def _try_direct_download(self, url: str, plant_name: str, year: Optional[str] = None) -> Optional[str]:
        """Try direct download"""
        try:
            # Create plant-specific directory
            plant_dir = os.path.join(self.download_dir, plant_name.replace(' ', '_'))
            os.makedirs(plant_dir, exist_ok=True)

            # Generate unique filename
            filename = self._generate_unique_filename(url, plant_name, year)
            file_path = os.path.join(plant_dir, filename)

            # Check if file already exists
            if os.path.exists(file_path):
                print(f"⏭️  Skipping (already exists): {filename}")
                return file_path

            if self.preferred_method == "direct":
                print(f"⬇️  Direct downloading: {filename}")
            else:
                print(f"🔄 Trying direct download as fallback...")

            # Direct download
            r = requests.get(url, stream=True, timeout=30, verify=False, headers=HEADERS)
            r.raise_for_status()

            return self._save_pdf_content(r, file_path, filename)

        except Exception as e:
            print(f"⚠️ Direct download failed: {e}")
            self.method_failures["direct"] += 1
            return None

    def _save_pdf_content(self, response, file_path: str, filename: str) -> Optional[str]:
        """Save PDF content from response"""
        try:
            # Get content length if available
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            with open(file_path, "wb") as f:
                start_time = time.time()
                last_update_time = start_time

                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        # Show progress every 2 seconds
                        current_time = time.time()
                        if current_time - last_update_time > 2:
                            if total_size > 0:
                                percent = (downloaded / total_size) * 100
                                print(f"   ⏳ Download progress: {percent:.1f}% ({downloaded/1024/1024:.1f} MB)")
                            else:
                                print(f"   ⏳ Downloaded: {downloaded/1024/1024:.1f} MB")
                            last_update_time = current_time

                        # Check for timeout (3 minutes max)
                        if current_time - start_time > 180:  # 3 minutes timeout
                            raise TimeoutError("Download taking too long (over 3 minutes)")

            print(f"✅ Downloaded: {file_path}")
            return file_path

        except Exception as e:
            print(f"❌ Failed to save PDF: {e}")
            # If file was partially downloaded, remove it
            if os.path.exists(file_path):
                os.remove(file_path)
            return None

    def download_pdf(self, url: str, plant_name: str, year: Optional[str] = None) -> Optional[str]:
        """Download a PDF from a URL.
        
        Args:
            url: URL of the PDF to download
            plant_name: Name of the power plant (for organizing files)
            year: Optional year to include in the filename
            
        Returns:
            Path to downloaded file if successful, None otherwise
        """
        try:
            # Create plant-specific directory
            plant_dir = os.path.join(self.download_dir, plant_name.replace(' ', '_'))
            os.makedirs(plant_dir, exist_ok=True)
            
            # Generate unique filename
            filename = self._generate_unique_filename(url, plant_name, year)
            
            # Full path for the downloaded file
            file_path = os.path.join(plant_dir, filename)
            
            # Check if file already exists
            if os.path.exists(file_path):
                print(f"⏭️  Skipping (already exists): {filename}")
                return file_path
            
            print(f"⬇️  Downloading: {url} to {file_path}")

            # Add retry logic for downloads
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # Use ScraperAPI to download the PDF to avoid blocking
                    if SCRAPER_API_KEY:
                        # Use ScraperAPI for downloading with proper URL encoding
                        encoded_url = urllib.parse.quote(url, safe=':/?#[]@!$&\'()*+,;=')
                        scraper_url = f"http://api.scraperapi.com?api_key={SCRAPER_API_KEY}&url={encoded_url}"
                        r = requests.get(scraper_url, stream=True, timeout=60, verify=False)
                    else:
                        # Fallback to direct download if no API key
                        r = requests.get(url, stream=True, timeout=30, verify=False, headers=HEADERS)
                    r.raise_for_status()
                    break  # Success, exit retry loop
                except (requests.exceptions.RequestException, requests.exceptions.Timeout) as e:
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # Exponential backoff
                        print(f"⚠️ Download attempt {attempt+1}/{max_retries} failed: {e}")
                        print(f"   Retrying in {wait_time} seconds...")
                        time.sleep(wait_time)
                    else:
                        print(f"❌ All {max_retries} ScraperAPI download attempts failed: {e}")
                        # Try direct download as fallback
                        if SCRAPER_API_KEY:  # Only try direct if we were using ScraperAPI
                            print("🔄 Trying direct download as fallback...")
                            try:
                                r = requests.get(url, stream=True, timeout=30, verify=False, headers=HEADERS)
                                r.raise_for_status()
                                break  # Success with direct download
                            except Exception as direct_e:
                                print(f"❌ Direct download also failed: {direct_e}")
                                return None
                        else:
                            return None

            try:
                # Get content length if available
                total_size = int(r.headers.get('content-length', 0))
                downloaded = 0

                with open(file_path, "wb") as f:
                    start_time = time.time()
                    last_update_time = start_time

                    for chunk in r.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)

                            # Show progress every 2 seconds
                            current_time = time.time()
                            if current_time - last_update_time > 2:
                                if total_size > 0:
                                    percent = (downloaded / total_size) * 100
                                    print(f"   ⏳ Download progress: {percent:.1f}% ({downloaded/1024/1024:.1f} MB)")
                                else:
                                    print(f"   ⏳ Downloaded: {downloaded/1024/1024:.1f} MB")
                                last_update_time = current_time
                                
                            # Check for timeout (3 minutes max)
                            if current_time - start_time > 180:  # 3 minutes timeout
                                raise TimeoutError("Download taking too long (over 3 minutes)")
                
                print(f"✅ Downloaded: {file_path}")
                return file_path
                
            except TimeoutError as te:
                print(f"⚠️ Download timeout: {te}")
                # If file was partially downloaded, remove it
                if os.path.exists(file_path):
                    os.remove(file_path)
                return None
                
            except requests.exceptions.RequestException as re:
                print(f"⚠️ Download request error: {re}")
                return None
            
        except Exception as e:
            print(f"❌ Failed to download {url}: {e}")
            return None
    
    def find_pdf_links(self, url: str) -> List[Dict[str, str]]:
        """Find all PDF links on a given webpage.
        
        Args:
            url: URL of the webpage to scrape
            
        Returns:
            List of dictionaries containing PDF link information
        """
        pdf_links = []
        
        try:
            print(f"🔍 Scanning {url} for PDF links...")
            
            # Get the page content using ScraperAPI if available with retry logic
            max_retries = 3
            response = None

            for attempt in range(max_retries):
                try:
                    if SCRAPER_API_KEY:
                        # Use ScraperAPI to get the page content with proper URL encoding
                        encoded_url = urllib.parse.quote(url, safe=':/?#[]@!$&\'()*+,;=')
                        scraper_url = f"http://api.scraperapi.com?api_key={SCRAPER_API_KEY}&url={encoded_url}"
                        response = requests.get(scraper_url, timeout=30, headers=HEADERS)
                    else:
                        # Fallback to direct request if no API key
                        response = requests.get(url, timeout=10, headers=HEADERS)
                    response.raise_for_status()
                    break  # Success, exit retry loop
                except (requests.exceptions.RequestException, requests.exceptions.Timeout) as e:
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # Exponential backoff
                        print(f"⚠️ Page request attempt {attempt+1}/{max_retries} failed: {e}")
                        print(f"   Retrying in {wait_time} seconds...")
                        time.sleep(wait_time)
                    else:
                        print(f"❌ All {max_retries} ScraperAPI page request attempts failed: {e}")
                        # Try direct request as fallback
                        if SCRAPER_API_KEY:  # Only try direct if we were using ScraperAPI
                            print("🔄 Trying direct page request as fallback...")
                            try:
                                response = requests.get(url, timeout=10, headers=HEADERS)
                                response.raise_for_status()
                                break  # Success with direct request
                            except Exception as direct_e:
                                print(f"❌ Direct page request also failed: {direct_e}")
                                return pdf_links
                        else:
                            return pdf_links
            
            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Find all links
            links = soup.find_all('a', href=True)
            print(f"   Found {len(links)} total links on the page")
            
            # Extract base URL for resolving relative links
            base_url = url.split("/")[0] + "//" + url.split("/")[2]
            
            pdf_count = 0
            for link in links:
                try:
                    href = link['href']
                    text = link.text.strip()
                    
                    # Check if it's a PDF link
                    if href and ('.pdf' in href.lower() or 'pdf' in href.lower()):
                        pdf_count += 1
                        print(f"   PDF link {pdf_count}: {href[:80]}... | Text: '{text[:30]}...'")
                        
                        if self._is_likely_annual_report_pdf(href, text):
                            # Convert relative URLs to absolute
                            if href.startswith("/"):
                                absolute_url = base_url + href
                            elif not href.startswith("http"):
                                absolute_url = base_url + "/" + href
                            else:
                                absolute_url = href

                            pdf_info = {
                                "url": absolute_url,
                                "text": text,
                                "filename": self._extract_filename(absolute_url, text)
                            }
                            pdf_links.append(pdf_info)
                            print(f"      ✅ Accepted PDF for download")
                        else:
                            print(f"      ❌ Rejected - not annual report related")
                            
                except Exception as e:
                    continue  # Skip problematic links
            
            if pdf_count == 0:
                print("   ⚠️  No PDF links found on this page")
            
            print(f"📄 Found {len(pdf_links)} PDF files to download")
            
        except Exception as e:
            print(f"❌ Error scanning {url}: {str(e)}")
        
        return pdf_links
    
    def _is_likely_annual_report_pdf(self, href: str, text: str) -> bool:
        """Check if a link is likely an annual report PDF.

        Args:
            href: The href attribute of the link
            text: The visible text of the link

        Returns:
            True if the link is likely an annual report PDF
        """
        # Check if URL ends with .pdf or contains pdf
        href_lower = href.lower()
        if not (href_lower.endswith('.pdf') or 'pdf' in href_lower):
            return False

        # DOWNLOAD ALL PDFs - Accept any PDF link
        # This allows users to manually check which ones are relevant
        return True

        # Original filtering logic (commented out):
        # Keywords that indicate annual reports
        # annual_report_keywords = [
        #     'annual report', 'annual', 'yearly report', 'form 10-k', '10-k',
        #     'financial report', 'financial statement', 'investor report',
        #     'sustainability report', 'corporate report', '2024', '2023', '2022', '2021', '2020'
        # ]
        #
        # # Check both URL and link text for keywords
        # combined_text = f"{href} {text}".lower()
        #
        # # More flexible matching - if it's a PDF and has any relevant keywords
        # has_keywords = any(keyword in combined_text for keyword in annual_report_keywords)
        #
        # # Also accept PDFs that are clearly in annual report sections
        # in_annual_section = any(section in href_lower for section in [
        #     'annual', 'investor', 'financial', 'report'
        # ])
        #
        # return has_keywords or in_annual_section
    
    def _extract_filename(self, url: str, text: str) -> str:
        """Extract a meaningful filename for the PDF.
        
        Args:
            url: PDF URL
            text: Link text
            
        Returns:
            Suggested filename for the PDF
        """
        # Try to get filename from URL
        url_filename = os.path.basename(url)
        
        if url_filename and url_filename.endswith('.pdf'):
            return url_filename
        
        # Generate filename from text
        if text:
            # Clean the text to make it a valid filename
            clean_text = "".join(c for c in text if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_text = clean_text.replace(' ', '_')
            return f"{clean_text}.pdf"
        
        # Fallback filename
        return "annual_report.pdf"
    
    def download_pdfs_from_url(self, url: str, plant_name: str, limit: Optional[int] = None) -> List[str]:
        """Download PDFs from a webpage.
        
        Args:
            url: URL of the webpage to scrape
            plant_name: Name of the power plant
            limit: Maximum number of PDFs to download
            
        Returns:
            List of paths to downloaded PDF files
        """
        downloaded_files = []
        
        try:
            # Find PDF links on the page
            pdf_links = self.find_pdf_links(url)

            if not pdf_links:
                print(f"📄 Found 0 potential annual report PDFs")

            # Download each PDF using smart method
            count = 0
            for pdf_info in pdf_links:
                if limit is not None and count >= limit:
                    break

                file_path = self.download_pdf_smart(pdf_info["url"], plant_name)
                if file_path:
                    downloaded_files.append(file_path)
                    count += 1

        except Exception as e:
            print(f"❌ Error downloading PDFs from {url}: {str(e)}")

        return downloaded_files

    def try_advanced_scraping(self, urls: List[str], plant_name: str) -> List[str]:
        """Try advanced scraping using Playwright for JavaScript-heavy sites"""
        try:
            print("\n🚀 Trying advanced scraping with Playwright...")

            # Check if required packages are available
            try:
                import playwright
                import aiohttp
                import aiofiles
                import tenacity
            except ImportError as ie:
                print(f"❌ Missing package: {ie}")
                print("⚠️ Advanced scraping not available (missing dependencies)")
                print("Install with: pip install playwright aiohttp aiofiles tenacity")
                print("Then run: python -m playwright install")
                return self._try_alternative_pdf_download(urls, plant_name)

            from .universal_pdf_downloader import download_pdfs_advanced

            downloaded_files = download_pdfs_advanced(urls, plant_name, self.download_dir)

            if downloaded_files:
                print(f"✅ Advanced scraping found {len(downloaded_files)} PDFs!")
                return downloaded_files
            else:
                print("❌ Advanced scraping also found no PDFs")
                return []

        except ImportError as ie:
            print(f"⚠️ Import error: {ie}")
            print("⚠️ Advanced scraping not available (missing dependencies)")
            print("Install with: pip install playwright aiohttp aiofiles tenacity")
            print("Then run: python -m playwright install")
            return self._try_alternative_pdf_download(urls, plant_name)
        except Exception as e:
            print(f"❌ Advanced scraping failed: {e}")
            return []

    def _try_alternative_pdf_download(self, urls: List[str], plant_name: str) -> List[str]:
        """Alternative PDF download method when advanced scraping is not available"""
        print("\n🔄 Trying alternative PDF download methods...")
        downloaded_files = []

        for url in urls:
            try:
                # Check if the URL itself is a PDF
                if url.lower().endswith('.pdf'):
                    print(f"📄 Direct PDF URL detected: {url}")
                    file_path = self.download_pdf_smart(url, plant_name)
                    if file_path:
                        downloaded_files.append(file_path)
                        print(f"✅ Downloaded direct PDF: {os.path.basename(file_path)}")
                    continue

                # Try to find PDF links with more aggressive pattern matching
                print(f"🔍 Trying aggressive PDF search on: {url}")
                response = None

                # Try ScraperAPI first
                if SCRAPER_API_KEY:
                    try:
                        encoded_url = urllib.parse.quote(url, safe=':/?#[]@!$&\'()*+,;=')
                        scraper_url = f"http://api.scraperapi.com?api_key={SCRAPER_API_KEY}&url={encoded_url}"
                        response = requests.get(scraper_url, timeout=30, headers=HEADERS)
                        response.raise_for_status()
                    except Exception as e:
                        print(f"   ⚠️ ScraperAPI failed: {e}")

                # Fallback to direct request
                if not response:
                    try:
                        response = requests.get(url, timeout=10, headers=HEADERS)
                        response.raise_for_status()
                    except Exception as e:
                        print(f"   ⚠️ Direct request failed: {e}")
                        continue

                # Parse HTML and look for any PDF-related patterns
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, "html.parser")

                # Look for PDF links in various attributes
                pdf_patterns = [
                    'a[href*=".pdf"]',
                    'a[href*="download"]',
                    'a[href*="report"]',
                    'a[href*="annual"]',
                    'iframe[src*=".pdf"]',
                    'embed[src*=".pdf"]',
                    'object[data*=".pdf"]'
                ]

                base_url = url.split("/")[0] + "//" + url.split("/")[2]
                found_pdfs = 0

                for pattern in pdf_patterns:
                    elements = soup.select(pattern)
                    for element in elements:
                        for attr in ['href', 'src', 'data']:
                            pdf_url = element.get(attr)
                            if pdf_url and '.pdf' in pdf_url.lower():
                                # Convert to absolute URL
                                if pdf_url.startswith("/"):
                                    pdf_url = base_url + pdf_url
                                elif not pdf_url.startswith("http"):
                                    pdf_url = base_url + "/" + pdf_url

                                print(f"   📄 Found PDF: {pdf_url}")
                                file_path = self.download_pdf_smart(pdf_url, plant_name)
                                if file_path:
                                    downloaded_files.append(file_path)
                                    found_pdfs += 1
                                    print(f"   ✅ Downloaded: {os.path.basename(file_path)}")

                if found_pdfs == 0:
                    print(f"   ❌ No PDFs found on {url}")

            except Exception as e:
                print(f"   ❌ Error processing {url}: {e}")
                continue

        if downloaded_files:
            print(f"✅ Alternative method found {len(downloaded_files)} PDFs!")
        else:
            print("❌ Alternative method also found no PDFs")

        return downloaded_files

    def scrape_annual_reports(self, urls: List[str], plant_name: str) -> List[str]:
        """Scrape annual report PDFs from a list of URLs.

        Args:
            urls: List of URLs to scrape for PDFs
            plant_name: Name of the power plant

        Returns:
            List of paths to downloaded PDF files
        """
        downloaded_files = []
        
        for url in urls:
            print(f"\n🔍 Searching for PDFs on: {url}")

            # Find and download PDFs from this URL
            page_files = self.download_pdfs_from_url(url, plant_name)
            downloaded_files.extend(page_files)

        # If no PDFs found with current methods, try advanced scraping
        if not downloaded_files:
            print("\n🔄 No PDFs found with standard methods, trying advanced scraping...")
            advanced_files = self.try_advanced_scraping(urls, plant_name)
            downloaded_files.extend(advanced_files)
        else:
            print(f"\n✅ Standard methods found {len(downloaded_files)} PDFs, skipping advanced scraping")

        return downloaded_files
    
    def run_downloader(self, plant_name: str, from_year: int = 2020, to_year: int = 2024) -> List[str]:
        """Run the PDF downloader for a specific plant and year range.
        
        Args:
            plant_name: Name of the power plant
            from_year: Start year for annual reports
            to_year: End year for annual reports
            
        Returns:
            List of paths to downloaded PDF files
        """
        downloaded_files = []
        any_pdf_downloaded = False
        
        # Try direct PDF search for each year
        print(f"\n🔍 Searching for {plant_name} annual reports from {from_year} to {to_year}")
        
        # Generic query variations for ALL power plants (no hardcoding)
        print(f"🔍 Using generic search patterns for: {plant_name}")
        query_variations = [
            # Try with "power plant" in the query
            lambda year: f'"{plant_name}" power plant annual report {year} filetype:pdf',
            # Try without "power plant" in the query (for holding companies)
            lambda year: f'"{plant_name}" annual report {year} filetype:pdf',
            # Try with "financial report" instead of "annual report"
            lambda year: f'"{plant_name}" financial report {year} filetype:pdf',
            # Try with "sustainability report"
            lambda year: f'"{plant_name}" sustainability report {year} filetype:pdf',
            # Try with "consolidated" for parent companies
            lambda year: f'"{plant_name}" consolidated annual report {year} filetype:pdf',
            # Try with "standalone" for subsidiary reports
            lambda year: f'"{plant_name}" standalone annual report {year} filetype:pdf'
        ]
        
        # Try each year with each query variation
        for year in range(to_year, from_year - 1, -1):  # Start with most recent year
            print(f"\n🔍 Searching for {plant_name} - {year} annual report")
            year_found = False
            
            for i, query_func in enumerate(query_variations):
                if year_found:
                    break
                    
                query = query_func(year)
                print(f"   Query variation {i+1}: {query}")
                
                # Try to find the PDF URL using the query
                pdf_url = self.search_pdf_url(query)
                
                if pdf_url:
                    # If PDF is found, download it
                    print(f"   ✅ Found PDF URL: {pdf_url}")
                    file_path = self.download_pdf_smart(pdf_url, plant_name, str(year))
                    if file_path:
                        downloaded_files.append(file_path)
                        any_pdf_downloaded = True
                        year_found = True
                        print(f"   ✅ Successfully downloaded {year} annual report")
                    else:
                        print(f"   ⚠️ Found URL but download failed: {pdf_url}")
                else:
                    print(f"   ⚠️ No PDF found with this query variation")
            
            if not year_found:
                print(f"⚠️ No annual report found for {year} after trying all query variations")
            
            # Small delay between years to avoid rate limiting
            time.sleep(1)
        
        # If no PDFs found from direct search, try scraping
        if not any_pdf_downloaded:
            print("\n⚠️ No PDFs found via direct search. Trying page scraping...")
            
            # Try different query variations for finding investor relations pages
            page_query_variations = [
                f'"{plant_name}" Investor Relations Annual Report',
                f'"{plant_name}" Financial Reports',
                f'"{plant_name}" Annual Reports',
                f'"{plant_name}" Sustainability Reports'
            ]
            
            all_page_urls = []
            
            for query in page_query_variations:
                print(f"\n🔍 Searching for pages with: {query}")
                
                params = {
                    "api_key": SCRAPER_API_KEY,
                    "query": query,
                    "num": 5
                }
                
                try:
                    response = requests.get(SCRAPER_API_URL, params=params, headers=HEADERS)
                    response.raise_for_status()
                    results = response.json().get("organic_results", [])
                    
                    # Get valid URLs to scrape
                    for result in results:
                        url = result.get("link", "")
                        if url.startswith("http") and url not in all_page_urls:
                            all_page_urls.append(url)
                    
                    # If we found some URLs, no need to try more query variations
                    if len(all_page_urls) >= 3:
                        break
                        
                except Exception as e:
                    print(f"❌ Error during URL search: {e}")
                    continue
            
            if all_page_urls:
                print(f"📊 Found {len(all_page_urls)} pages to scrape")
                
                # Try each URL
                for i, url in enumerate(all_page_urls[:5]):  # Limit to top 5 results
                    print(f"\n🔍 Trying page {i+1}/{min(5, len(all_page_urls))}: {url}")
                    page_files = self.download_pdfs_from_url(url, plant_name, 5)
                    if page_files:
                        downloaded_files.extend(page_files)
                        print(f"✅ Found {len(page_files)} PDFs on {url}")
                        if len(downloaded_files) >= 3:  # If we found at least 3 PDFs, that's enough
                            break
                    else:
                        print(f"❌ No PDFs found on {url}")
            else:
                print("❌ No suitable pages found for scraping")
        
        # If no PDFs found from direct search and page scraping, try advanced scraping
        if not downloaded_files:
            print("\n🔄 No PDFs found with standard methods, trying advanced scraping...")

            # Collect all URLs we tried to scrape
            all_urls_tried = []
            if all_page_urls:
                all_urls_tried.extend(all_page_urls[:5])  # Use the URLs we already found

            # If we don't have any URLs, try to find some basic ones
            if not all_urls_tried:
                # Try a simple search to find the company's main website
                basic_query = f'"{plant_name}" site:*.com OR site:*.org'
                try:
                    params = {
                        "api_key": SCRAPER_API_KEY,
                        "query": basic_query,
                        "num": 3
                    }
                    response = requests.get(SCRAPER_API_URL, params=params, headers=HEADERS)
                    response.raise_for_status()
                    results = response.json().get("organic_results", [])

                    for result in results:
                        url = result.get("link", "")
                        if url.startswith("http"):
                            all_urls_tried.append(url)
                except Exception as e:
                    print(f"❌ Error finding basic URLs: {e}")

            # Try advanced scraping if we have URLs
            if all_urls_tried:
                advanced_files = self.try_advanced_scraping(all_urls_tried, plant_name)
                downloaded_files.extend(advanced_files)
            else:
                print("❌ No URLs available for advanced scraping")

        # Return unique list of downloaded files
        return list(set(downloaded_files))