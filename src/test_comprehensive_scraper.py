#!/usr/bin/env python3
"""
Test script for the Comprehensive Automated Scraper

This script demonstrates the new comprehensive scraper functionality:
1. Generic agent-based search (no hardcoding)
2. Proper fallback order: ScraperAPI → Playwright → Selenium → Direct
3. Automatic PDF scraping without permission prompts
4. OpenAI classification and auto-deletion of non-consolidated reports
"""

import os
import sys
from dotenv import load_dotenv

# Add the src directory to the path
sys.path.insert(0, os.path.dirname(__file__))

from agent.comprehensive_automated_scraper import ComprehensiveAutomatedScraper

# Load environment variables
load_dotenv()


def test_comprehensive_scraper():
    """Test the comprehensive automated scraper."""
    print("🧪 TESTING COMPREHENSIVE AUTOMATED SCRAPER")
    print("=" * 60)
    
    # Check required environment variables
    required_vars = ["GEMINI_API_KEY", "OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these variables before running the test.")
        return False
    
    # Optional but recommended
    if not os.getenv("SCRAPER_API_KEY"):
        print("⚠️ Warning: SCRAPER_API_KEY not set (ScraperAPI fallback will be limited)")
    
    print("✅ All required environment variables are set")
    
    # Test with a sample power plant
    test_plant = "San Miguel Power Plant"
    print(f"\n🏭 Testing with: {test_plant}")
    
    try:
        # Initialize the comprehensive scraper
        scraper = ComprehensiveAutomatedScraper(download_dir="./test_downloads")
        
        # Run the comprehensive automation
        results = scraper.run_comprehensive_automation(test_plant)
        
        # Print test results
        print("\n" + "=" * 60)
        print("🧪 TEST RESULTS")
        print("=" * 60)
        
        if results["success"]:
            print("✅ Test PASSED - Comprehensive automation completed successfully")
            print(f"📊 Statistics:")
            print(f"   • Duration: {results['duration_seconds']} seconds")
            print(f"   • Total PDFs Downloaded: {results['total_pdfs_downloaded']}")
            print(f"   • Valid Consolidated Reports: {results['valid_consolidated_reports']}")
            print(f"   • Deleted Non-Consolidated: {results['deleted_non_consolidated']}")
            
            print(f"\n📈 Method Success Stats:")
            stats = results['method_stats']
            print(f"   • ScraperAPI: {stats['scraperapi_success']} PDFs")
            print(f"   • Playwright: {stats['playwright_success']} PDFs")
            print(f"   • Selenium: {stats['selenium_success']} PDFs")
            print(f"   • Direct: {stats['direct_success']} PDFs")
            
            if results['final_pdf_paths']:
                print(f"\n📁 Final PDFs:")
                for pdf_path in results['final_pdf_paths']:
                    print(f"   📄 {os.path.basename(pdf_path)}")
            
            return True
        else:
            print("❌ Test FAILED - Comprehensive automation failed")
            print(f"Error: {results.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Test FAILED with exception: {str(e)}")
        return False


def test_individual_components():
    """Test individual components of the comprehensive scraper."""
    print("\n🔧 TESTING INDIVIDUAL COMPONENTS")
    print("=" * 60)
    
    try:
        scraper = ComprehensiveAutomatedScraper(download_dir="./test_downloads")
        
        # Test 1: Generic search
        print("\n🧪 Test 1: Generic Search")
        search_results = scraper.generic_search_for_annual_reports("Test Power Plant")
        if search_results.get("sources_gathered"):
            print("✅ Generic search working")
        else:
            print("⚠️ Generic search returned no sources (may be expected)")
        
        # Test 2: PDF text extraction (if we have a sample PDF)
        print("\n🧪 Test 2: PDF Text Extraction")
        # This would require a sample PDF file
        print("⏭️ Skipped (requires sample PDF)")
        
        # Test 3: OpenAI classification (with sample text)
        print("\n🧪 Test 3: OpenAI Classification")
        sample_text = """
        ANNUAL REPORT 2023
        Test Power Plant Company
        
        CONSOLIDATED FINANCIAL STATEMENTS
        
        This annual report contains both standalone and consolidated financial statements
        for Test Power Plant Company for the year ended December 31, 2023.
        """
        
        is_consolidated = scraper.classify_pdf_as_consolidated(sample_text, "Test Power Plant")
        if is_consolidated:
            print("✅ OpenAI classification working (classified as consolidated)")
        else:
            print("⚠️ OpenAI classification working (classified as non-consolidated)")
        
        print("\n✅ Individual component tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Component test failed: {str(e)}")
        return False


def main():
    """Main test function."""
    print("🚀 COMPREHENSIVE AUTOMATED SCRAPER TEST SUITE")
    print("=" * 60)
    
    # Test individual components first
    component_test_passed = test_individual_components()
    
    if component_test_passed:
        print("\n" + "=" * 60)
        
        # Ask user if they want to run the full test
        run_full_test = input("Run full comprehensive test? (y/n): ").strip().lower()
        
        if run_full_test in ['y', 'yes']:
            full_test_passed = test_comprehensive_scraper()
            
            if full_test_passed:
                print("\n🎉 ALL TESTS PASSED!")
            else:
                print("\n❌ Some tests failed")
        else:
            print("\n⏭️ Skipped full comprehensive test")
    else:
        print("\n❌ Component tests failed, skipping full test")
    
    print("\n👋 Test suite completed")


if __name__ == "__main__":
    main()
