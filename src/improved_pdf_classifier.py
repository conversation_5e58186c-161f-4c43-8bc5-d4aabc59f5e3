#!/usr/bin/env python3
"""
Improved PDF Classification System for Power Plant Annual Reports

This module provides enhanced classification logic that:
1. Keeps standalone reports (deletes consolidated)
2. If consolidated available → deletes it
3. If both standalone + consolidated in single PDF → keeps it
"""

import os
from pathlib import Path
from openai import OpenAI
from dotenv import load_dotenv

# Load API key from .env
load_dotenv()

# Initialize OpenAI client with robust error handling
def initialize_openai_client():
    """Initialize OpenAI client with multiple fallback strategies"""
    api_key = os.getenv("OPENAI_API_KEY")

    if not api_key:
        print("⚠️ OPENAI_API_KEY not found in environment variables")
        return None

    # Strategy 1: Standard initialization
    try:
        client = OpenAI(api_key=api_key)
        # Test the client with a simple call
        client.models.list()
        print("✅ OpenAI client initialized successfully")
        return client
    except Exception as e:
        print(f"⚠️ Standard OpenAI initialization failed: {e}")

    # Strategy 2: Minimal initialization
    try:
        client = OpenAI()
        client.api_key = api_key
        # Test the client
        client.models.list()
        print("✅ OpenAI client initialized with fallback method")
        return client
    except Exception as e:
        print(f"⚠️ Fallback OpenAI initialization failed: {e}")

    # Strategy 3: Environment variable approach
    try:
        os.environ["OPENAI_API_KEY"] = api_key
        client = OpenAI()
        # Test the client
        client.models.list()
        print("✅ OpenAI client initialized with environment variable")
        return client
    except Exception as e:
        print(f"⚠️ Environment variable OpenAI initialization failed: {e}")

    print("❌ All OpenAI initialization strategies failed. Using fallback classification.")
    return None

# Initialize the client
client = initialize_openai_client()


def _enhanced_fallback_classification(text: str, plant_name: str) -> bool:
    """Enhanced fallback classification when OpenAI is not available.

    Uses keyword analysis and heuristics to determine if a PDF should be kept.

    Args:
        text: Extracted text from PDF
        plant_name: Name of the power plant

    Returns:
        True if PDF should be kept, False if it should be deleted
    """
    text_lower = text.lower()
    plant_lower = plant_name.lower()

    # Extract key words from plant name for matching
    plant_words = [word.strip() for word in plant_lower.replace(',', ' ').split() if len(word.strip()) > 2]

    # Check for annual report indicators
    annual_indicators = [
        'annual report', 'yearly report', 'annual financial', 'financial report',
        'annual statement', 'consolidated annual', 'standalone annual',
        'form 10-k', 'form 20-f', 'sec filing', 'investor relations'
    ]

    has_annual_indicator = any(indicator in text_lower for indicator in annual_indicators)

    # Check for plant/company name match
    plant_match_score = sum(1 for word in plant_words if word in text_lower)
    has_plant_match = plant_match_score >= max(1, len(plant_words) // 2)  # At least half the words match

    # Check for consolidated vs standalone indicators
    consolidated_indicators = ['consolidated', 'group', 'holding', 'parent company']
    standalone_indicators = ['standalone', 'separate', 'individual', 'subsidiary']

    has_consolidated = any(indicator in text_lower for indicator in consolidated_indicators)
    has_standalone = any(indicator in text_lower for indicator in standalone_indicators)

    # Decision logic (same as OpenAI version)
    if has_annual_indicator and has_plant_match:
        if has_standalone and has_consolidated:
            print("✅ KEEP: Contains both standalone and consolidated (enhanced fallback)")
            return True
        elif has_standalone and not has_consolidated:
            print("✅ KEEP: Contains standalone reports only (enhanced fallback)")
            return True
        elif has_consolidated and not has_standalone:
            print("🗑️ DELETE: Contains only consolidated reports (enhanced fallback)")
            return False
        else:
            # No clear indication, but it's an annual report for the right company
            print("✅ KEEP: Annual report for correct company, type unclear (enhanced fallback)")
            return True
    elif has_annual_indicator:
        print("⚠️ KEEP: Annual report but company match unclear (enhanced fallback)")
        return True  # Conservative approach - keep if it's an annual report
    elif has_plant_match:
        print("⚠️ KEEP: Company match but document type unclear (enhanced fallback)")
        return True  # Conservative approach - keep if it matches the company
    else:
        print("🗑️ DELETE: No clear annual report or company indicators (enhanced fallback)")
        return False


def get_pdf_file_paths(plant_name, base_dir="downloads"):
    """Get all PDF file paths for a given plant name."""
    folder_name = plant_name.replace(" ", "_").replace('"', '')
    plant_dir = Path("downloads") / folder_name
 
    if not os.path.isdir(plant_dir):
        raise FileNotFoundError(f"Directory not found: {plant_dir}")
 
    pdf_paths = [
        str(file_path)
        for file_path in plant_dir.glob("*.pdf")
    ]
    
    return pdf_paths


def extract_first_5_pages_text_langchain(pdf_path, max_pages=5, languages=["eng"]):
    """Extract text from first N pages of PDF using LangChain."""
    try:
        # Try different import paths for LangChain
        try:
            from langchain_community.document_loaders import UnstructuredPDFLoader
        except ImportError:
            from langchain.document_loaders import UnstructuredPDFLoader

        loader = UnstructuredPDFLoader(
            file_path=pdf_path,
            strategy="auto",  # auto-detect scanned or digital
            languages=["eng"]
        )
        documents = loader.load()

        # Combine content of only first N pages
        text = ""
        for i, doc in enumerate(documents):
            if i >= max_pages:
                break
            text += doc.page_content + "\n"

        return text.strip()

    except Exception as e:
        print(f"Error extracting {pdf_path}: {e}")
        return ""


def is_valid_annual_report(text, plant_name, model="gpt-4o"):
    """
    Enhanced classification logic for annual reports.

    Decision Rules:
    1. Keep standalone reports (delete consolidated)
    2. If consolidated available → delete it
    3. If both standalone + consolidated in single PDF → keep it
    """
    # Check if OpenAI client is available
    if client is None:
        print("⚠️ OpenAI client not available, using enhanced fallback classification")
        return _enhanced_fallback_classification(text, plant_name)

    system_prompt = (
        "You are a document classification assistant. Your task is to check whether a given document "
        "is an annual report and whether it belongs to the specified power plant. Additionally, determine "
        "the type of financial reports included (standalone, consolidated, or both)."
    )

    user_prompt = f"""
    You are given the beginning of a document. Please answer YES or NO only for each question.

    1. Is this document an *annual report*?
    2. Does this annual report belong to the power plant named "{plant_name}"?
    3. Does the document include *standalone* financial reports?
    4. Does the document include *consolidated* financial reports?
    5. Does the document include *both standalone and consolidated* financial reports in the same PDF?

    Respond strictly in this format:
    Annual report: YES or NO along with the reason  
    Belongs to plant: YES or NO along with the reason  
    Has standalone: YES or NO along with the reason
    Has consolidated: YES or NO along with the reason
    Has both in same PDF: YES or NO along with the reason

    Here is the text:
    {text[:100000]}

    NOTE: The power plant name may be abbreviated or phrased differently. Use judgment in matching it.
    """

    try:
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0
        )

        content = response.choices[0].message.content
        print("🔍 GPT-4o Reasoning:\n" + content.strip() + "\n")

        # Convert to lowercase for consistent checks
        content_lower = content.lower()

        # Extract flags
        is_annual = "annual report: yes" in content_lower
        is_correct_plant = "belongs to plant: yes" in content_lower
        has_standalone = "has standalone: yes" in content_lower
        has_consolidated = "has consolidated: yes" in content_lower
        has_both_in_same = "has both in same pdf: yes" in content_lower

        # Decision logic based on your requirements:
        # 1. Keep standalone reports (delete consolidated)
        # 2. If consolidated available → delete it
        # 3. If both standalone + consolidated in single PDF → keep it
        
        if not (is_annual and is_correct_plant):
            print("❌ Not a valid annual report for this plant")
            return False
            
        if has_both_in_same:
            print("✅ KEEP: Contains both standalone and consolidated in same PDF")
            return True
        elif has_standalone and not has_consolidated:
            print("✅ KEEP: Contains standalone reports only")
            return True
        elif has_consolidated and not has_standalone:
            print("🗑️ DELETE: Contains only consolidated reports")
            return False
        else:
            print("⚠️ UNCERTAIN: Could not determine report type clearly, keeping as fallback")
            return True  # Keep uncertain cases to be safe

    except Exception as e:
        print(f"❌ Error verifying report: {e}")
        return False


def delete_if_invalid(flag: bool, pdf_path: str):
    """Delete PDF if it doesn't meet the criteria."""
    if flag:
        print(f"✅ Keeping: {pdf_path}")
        return True
    else:
        try:
            os.remove(pdf_path)
            print(f"🗑️ Deleted: {pdf_path}")
            return False
        except Exception as e:
            print(f"❌ Failed to delete {pdf_path}: {e}")
            return False


def process_pdf_list_with_validation(pdf_list, plant_name: str, model: str = "gpt-4o", max_pages: int = 5):
    """
    Process a list of PDFs with improved validation logic.
    
    Args:
        pdf_list: List of PDF file paths
        plant_name: Name of the power plant
        model: OpenAI model to use for classification
        max_pages: Number of pages to extract for analysis
    """
    print(f"\n🔍 Starting PDF classification for {plant_name}")
    print(f"📋 Found {len(pdf_list)} PDFs to process")
    print("📝 Classification Rules:")
    print("   ✅ KEEP: Standalone reports")
    print("   ✅ KEEP: PDFs with both standalone + consolidated")
    print("   🗑️ DELETE: Consolidated-only reports")
    print("-" * 60)
    
    kept_files = []
    deleted_files = []
    
    for each_pdf in pdf_list:
        print(f"\n📄 Processing: {os.path.basename(each_pdf)}")
        text = extract_first_5_pages_text_langchain(each_pdf, max_pages=max_pages)
        
        if not text:
            print("⚠️ Could not extract text, keeping file as fallback")
            kept_files.append(each_pdf)
            continue
            
        validation_flag = is_valid_annual_report(text, plant_name, model=model)
        
        if delete_if_invalid(validation_flag, each_pdf):
            kept_files.append(each_pdf)
        else:
            deleted_files.append(each_pdf)
    
    print("\n" + "="*60)
    print("📊 CLASSIFICATION SUMMARY:")
    print(f"✅ Kept: {len(kept_files)} files")
    print(f"🗑️ Deleted: {len(deleted_files)} files")
    
    if kept_files:
        print("\n✅ KEPT FILES:")
        for file in kept_files:
            print(f"   📄 {os.path.basename(file)}")
    
    if deleted_files:
        print("\n🗑️ DELETED FILES:")
        for file in deleted_files:
            print(f"   📄 {os.path.basename(file)}")
    
    print("="*60)


# Example usage function
def classify_pdfs_for_plant(plant_name: str):
    """
    Main function to classify PDFs for a given power plant.
    
    Args:
        plant_name: Name of the power plant
    """
    try:
        pdf_list = get_pdf_file_paths(plant_name)
        process_pdf_list_with_validation(pdf_list, plant_name)
    except FileNotFoundError as e:
        print(f"❌ {e}")
    except Exception as e:
        print(f"❌ Error processing PDFs: {e}")


if __name__ == "__main__":
    # Example usage
    plant_name = "SEIL Energy India Limited"
    classify_pdfs_for_plant(plant_name)
