#!/usr/bin/env python3
"""
Cleanup script to remove unused files from the power plant scraper
"""

import os
import shutil
from pathlib import Path

def cleanup_unused_files():
    """Remove all unused files from the project"""
    
    print("🧹 CLEANING UP UNUSED FILES")
    print("=" * 50)
    
    # Files and directories to remove
    files_to_remove = [
        # Redundant classifiers
        "src/improved_pdf_classifier.py",
        "src/integrated_power_plant_scraper.py",
        
        # Unused scrapers
        "src/agent/pdf_scraper.py", 
        "src/agent/universal_pdf_downloader.py",
        "src/agent/content_analyzer.py",
        
        # Test/debug files
        "src/test_comprehensive_scraper.py",
        "src/example_usage.py",
        "test_comprehensive_scraper_fix.py",
        "test_fast_classification.py",
        "test_fixed_openai.py",
        "test_google_genai_fix.py",
        "test_graph_import.py", 
        "test_improved_classification.py",
        "test_openai_fix.py",
        "test_simple_openai_on_existing.py",
        "debug_import_order.py",
        "debug_scraper_url.py",
        "fix_nltk_data.py",
        "fix_openai_client.py",
        "minimal_openai_test.py",
        
        # Alternative approaches
        "src/agent/app.py",
        "1.py",
        
        # Build/cache directories
        "src/__pycache__",
        "src/agent/__pycache__", 
        "src/agent.egg-info",
        "build",
        
        # Example directories
        "examples",
    ]
    
    removed_count = 0
    kept_count = 0
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"🗑️ Removed directory: {file_path}")
                else:
                    os.remove(file_path)
                    print(f"🗑️ Removed file: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"⚠️ Could not remove {file_path}: {e}")
        else:
            print(f"⚪ Already gone: {file_path}")
    
    print(f"\n📊 CLEANUP SUMMARY:")
    print(f"🗑️ Removed: {removed_count} files/directories")
    
    # Show what's left (essential files)
    print(f"\n✅ ESSENTIAL FILES REMAINING:")
    essential_files = [
        "src/power_plant_search.py",
        "src/simple_openai_classifier.py", 
        "src/agent/comprehensive_automated_scraper.py",
        "src/agent/scraper_api_pdf_scraper.py",
        "src/agent/graph.py",
        "src/agent/configuration.py",
        "src/agent/state.py", 
        "src/agent/prompts.py",
        "src/agent/utils.py",
        "src/agent/tools_and_schemas.py",
        "src/agent/__init__.py"
    ]
    
    for file_path in essential_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
            kept_count += 1
        else:
            print(f"   ❌ MISSING: {file_path}")
    
    print(f"\n🎯 FINAL RESULT:")
    print(f"   Essential files: {kept_count}")
    print(f"   Total cleanup: {removed_count} removed")
    print(f"   Clean, minimal codebase achieved! 🎉")

if __name__ == "__main__":
    response = input("⚠️ This will permanently delete unused files. Continue? (y/N): ")
    if response.lower() == 'y':
        cleanup_unused_files()
    else:
        print("❌ Cleanup cancelled")
