# Comprehensive Automated Power Plant Scraper

## Overview

The Comprehensive Automated Scraper is a fully automated system for finding, downloading, and validating power plant annual reports. It addresses all the key requirements:

### ✅ Key Features

1. **🔍 Generic Agent-Based Search** - No hardcoded company logic, uses AI agents to discover sources
2. **📋 Proper Fallback Order** - ScraperAPI → Playwright → Selenium → Direct (no more incorrect priorities)
3. **🤖 Fully Automated** - No user prompts, automatically scrapes PDFs after finding sources
4. **🧠 OpenAI Classification** - Automatically classifies and deletes non-consolidated annual reports
5. **📊 Comprehensive Statistics** - Tracks success rates for each method

## Architecture

### Fallback Chain (Fixed Priority Order)

```
1. ScraperAPI (Primary)
   ↓ (if no PDFs found)
2. Playwright (Advanced JS handling)
   ↓ (if no PDFs found)  
3. Selenium (Browser automation)
   ↓ (if no PDFs found)
4. Direct Search (Fallback search)
```

### Components

- **`ComprehensiveAutomatedScraper`** - Main orchestrator class
- **Generic Search System** - Uses existing `power_plant_graph` without hardcoding
- **Multi-Method PDF Scraping** - Proper fallback implementation
- **OpenAI Classification** - Validates PDFs as consolidated annual reports
- **Automatic Cleanup** - Deletes invalid PDFs without user intervention

## Installation

### 1. Install Dependencies

```bash
# Basic dependencies
pip install -r requirements-power-plant.txt

# Advanced scraper dependencies (for Playwright)
pip install playwright aiohttp aiofiles tenacity

# Install Playwright browsers
python -m playwright install
```

### 2. Environment Variables

Create a `.env` file:

```bash
# Required
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Optional (improves ScraperAPI performance)
SCRAPER_API_KEY=your_scraper_api_key_here
```

## Usage

### 1. Standalone Comprehensive Scraper

```bash
python src/agent/comprehensive_automated_scraper.py
```

### 2. Integrated with Power Plant Search

```bash
python src/power_plant_search.py
```

Choose option 2 for "Comprehensive Automated Mode"

### 3. Test Suite

```bash
python src/test_comprehensive_scraper.py
```

## How It Works

### Step 1: Generic AI-Powered Search
- Uses `power_plant_graph` with generic state initialization
- No hardcoded company names or URLs
- Discovers annual report sources dynamically
- Falls back to direct search if AI search fails

### Step 2: Multi-Method PDF Scraping
- **ScraperAPI First**: Handles most websites effectively
- **Playwright Second**: For JavaScript-heavy sites that ScraperAPI can't handle
- **Selenium Third**: For complex browser interactions
- **Direct Search Fourth**: Searches by company name and year range

### Step 3: Automatic Classification
- Extracts text from first 5 pages of each PDF
- Uses OpenAI GPT-4o to classify each PDF:
  - ✅ Is it an annual report?
  - ✅ Does it belong to the specified power plant?
  - ✅ Does it include both standalone AND consolidated financials?
- Automatically deletes PDFs that don't meet all criteria

## API Reference

### ComprehensiveAutomatedScraper

```python
from agent.comprehensive_automated_scraper import ComprehensiveAutomatedScraper

# Initialize
scraper = ComprehensiveAutomatedScraper(download_dir="./downloads")

# Run full automation
results = scraper.run_comprehensive_automation("Power Plant Name")

# Results structure
{
    "success": True,
    "plant_name": "Power Plant Name",
    "duration_seconds": 45.2,
    "total_pdfs_downloaded": 5,
    "valid_consolidated_reports": 2,
    "deleted_non_consolidated": 3,
    "final_pdf_paths": ["path1.pdf", "path2.pdf"],
    "method_stats": {
        "scraperapi_success": 3,
        "playwright_success": 2,
        "selenium_success": 0,
        "direct_success": 0
    }
}
```

### Individual Methods

```python
# Generic search (no hardcoding)
search_results = scraper.generic_search_for_annual_reports("Plant Name")

# Multi-method scraping with proper fallbacks
pdf_files = scraper.scrape_with_proper_fallbacks(sources, "Plant Name")

# Automatic classification and cleanup
valid_pdfs = scraper.auto_classify_and_delete_non_consolidated(pdf_files, "Plant Name")
```

## Improvements Over Previous System

### ❌ Before (Issues Fixed)
- Playwright was 3rd/4th fallback (too late)
- Hardcoded company logic (Pirkey, San Miguel, etc.)
- User prompts required for PDF scraping
- Manual PDF validation
- Inconsistent fallback order

### ✅ After (New System)
- Playwright is proper 2nd fallback
- Fully generic agent-based search
- Completely automated (no prompts)
- Automatic OpenAI classification
- Consistent, logical fallback order

## Statistics and Monitoring

The system tracks detailed statistics:

- **Method Success Rates**: Which scraping method found PDFs
- **Classification Results**: How many PDFs were kept vs deleted
- **Processing Time**: Total duration for complete automation
- **Error Tracking**: Detailed error reporting for each method

## Error Handling

- **Graceful Degradation**: If one method fails, automatically tries the next
- **Comprehensive Logging**: Detailed output for debugging
- **Environment Validation**: Checks for required API keys
- **Exception Recovery**: Continues processing even if individual components fail

## Integration Points

### With Existing System
- Integrates seamlessly with existing `power_plant_search.py`
- Uses existing agent components (`power_plant_graph`, scrapers)
- Maintains backward compatibility

### With External APIs
- **Gemini API**: For AI-powered search
- **OpenAI API**: For PDF classification
- **ScraperAPI**: For reliable web scraping
- **Playwright**: For JavaScript-heavy sites

## Future Enhancements

- **Parallel Processing**: Scrape multiple sources simultaneously
- **Caching System**: Cache search results and classifications
- **Custom Classification Rules**: User-defined PDF validation criteria
- **Batch Processing**: Process multiple power plants in one run
- **API Interface**: REST API for programmatic access
