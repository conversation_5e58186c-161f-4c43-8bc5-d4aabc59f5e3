#!/usr/bin/env python3
"""
Test the simple OpenAI classifier on existing downloaded PDFs
"""

import os
import glob
from pathlib import Path

def find_existing_pdfs():
    """Find existing downloaded PDFs"""
    download_dirs = [
        "downloads/SEIL_Energy_India_Limited",
        "downloads/san_miguel_corporation",
        "downloads/PLTU_Suparma"
    ]
    
    all_pdfs = []
    for download_dir in download_dirs:
        if os.path.exists(download_dir):
            pdfs = glob.glob(f"{download_dir}/*.pdf")
            if pdfs:
                print(f"📁 Found {len(pdfs)} PDFs in {download_dir}")
                all_pdfs.extend(pdfs)
    
    return all_pdfs

def main():
    """Test simple OpenAI classifier on existing PDFs"""
    print("🧪 Testing Simple OpenAI Classifier on Existing PDFs")
    print("=" * 60)
    
    # Find existing PDFs
    existing_pdfs = find_existing_pdfs()
    
    if not existing_pdfs:
        print("❌ No existing PDFs found to test")
        return
    
    print(f"📋 Found {len(existing_pdfs)} total PDFs to test")
    
    # Test with a few PDFs (not all to avoid long processing)
    test_pdfs = existing_pdfs[:3]  # Test first 3 PDFs
    
    print(f"🔍 Testing with {len(test_pdfs)} PDFs:")
    for pdf in test_pdfs:
        print(f"   📄 {Path(pdf).name}")
    
    try:
        # Import the simple classifier
        from src.simple_openai_classifier import process_pdf_list_simple
        
        # Test classification
        plant_name = "Test Power Plant"
        result = process_pdf_list_simple(test_pdfs, plant_name)
        
        print(f"\n🎉 SUCCESS! OpenAI classification worked!")
        print(f"📊 Result: {len(result)} PDFs would be kept")
        
    except Exception as e:
        print(f"❌ Error testing simple OpenAI classifier: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
