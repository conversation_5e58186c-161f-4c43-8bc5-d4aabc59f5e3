#!/usr/bin/env python3
"""
Debug the import order to find what's causing the OpenAI client issue
"""

import os
import sys
from dotenv import load_dotenv

load_dotenv()

def test_openai_after_imports():
    """Test OpenAI client after importing modules in the same order as our app"""
    
    print("🔍 Testing OpenAI client with application import order...")
    
    # Test 1: OpenAI alone (we know this works)
    print("\n1. Testing OpenAI alone...")
    try:
        from openai import OpenAI
        client = OpenAI()
        print("✅ OpenAI alone works")
    except Exception as e:
        print(f"❌ OpenAI alone failed: {e}")
        return
    
    # Clear modules to start fresh
    modules_to_clear = [mod for mod in sys.modules.keys() if 'openai' in mod.lower()]
    for mod in modules_to_clear:
        del sys.modules[mod]
    
    # Test 2: Import langchain first (this might be the culprit)
    print("\n2. Testing after importing langchain...")
    try:
        from langchain_community.document_loaders import UnstructuredPDFLoader
        print("✅ LangChain imported")
        
        from openai import OpenAI
        client = OpenAI()
        print("✅ OpenAI works after <PERSON><PERSON><PERSON><PERSON>")
    except Exception as e:
        print(f"❌ OpenAI failed after <PERSON><PERSON>hain: {e}")
        if "proxies" in str(e):
            print("🎯 Found the culprit! LangChain is causing the proxies issue!")
            return "langchain"
    
    # Clear modules again
    modules_to_clear = [mod for mod in sys.modules.keys() if any(x in mod.lower() for x in ['openai', 'langchain'])]
    for mod in modules_to_clear:
        if mod in sys.modules:
            del sys.modules[mod]
    
    # Test 3: Import other modules one by one
    problematic_imports = []
    
    imports_to_test = [
        ("sys path modification", "sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))"),
        ("agent.graph", "from agent.graph import power_plant_graph"),
        ("agent.configuration", "from agent.configuration import Configuration"),
        ("agent.scraper_api_pdf_scraper", "from agent.scraper_api_pdf_scraper import ScraperAPIPDFScraper"),
        ("agent.content_analyzer", "from agent.content_analyzer import ContentAnalyzer"),
    ]
    
    for import_name, import_statement in imports_to_test:
        print(f"\n3.{len(problematic_imports)+1}. Testing after importing {import_name}...")
        
        try:
            # Execute the import
            exec(import_statement)
            print(f"✅ {import_name} imported")
            
            # Test OpenAI
            from openai import OpenAI
            client = OpenAI()
            print(f"✅ OpenAI works after {import_name}")
            
        except Exception as e:
            print(f"❌ OpenAI failed after {import_name}: {e}")
            if "proxies" in str(e):
                problematic_imports.append(import_name)
                print(f"🎯 {import_name} is causing the proxies issue!")
        
        # Clear OpenAI modules for next test
        modules_to_clear = [mod for mod in sys.modules.keys() if 'openai' in mod.lower()]
        for mod in modules_to_clear:
            if mod in sys.modules:
                del sys.modules[mod]
    
    return problematic_imports

def test_langchain_workaround():
    """Test if we can work around the LangChain issue"""
    print("\n🔧 Testing LangChain workaround...")
    
    try:
        # Import OpenAI BEFORE LangChain
        print("1. Importing OpenAI first...")
        from openai import OpenAI
        client = OpenAI()
        print("✅ OpenAI imported and working")
        
        # Now import LangChain
        print("2. Importing LangChain after OpenAI...")
        from langchain_community.document_loaders import UnstructuredPDFLoader
        print("✅ LangChain imported")
        
        # Test if OpenAI still works
        print("3. Testing if OpenAI still works...")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=5
        )
        print(f"✅ OpenAI still works! Response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ Workaround failed: {e}")
        return False

def main():
    """Main debug function"""
    print("🔧 OPENAI IMPORT ORDER DEBUGGER")
    print("=" * 60)
    
    # Test import order
    problematic = test_openai_after_imports()
    
    if problematic:
        print(f"\n🎯 Found problematic imports: {problematic}")
        
        if "langchain" in str(problematic).lower():
            print("\n💡 LangChain is likely causing the issue!")
            print("   LangChain sometimes monkey-patches HTTP clients")
            
            if test_langchain_workaround():
                print("\n✅ SOLUTION FOUND!")
                print("   Import OpenAI BEFORE LangChain to avoid conflicts")
            else:
                print("\n❌ Workaround failed, need alternative solution")
    else:
        print("\n✅ No problematic imports found")
    
    print("\n" + "=" * 60)
    print("Debug complete!")

if __name__ == "__main__":
    main()
