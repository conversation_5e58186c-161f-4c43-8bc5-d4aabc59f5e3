#!/usr/bin/env python3
"""
Test fast classification on a few PDFs
"""

import os
import glob
import sys
from pathlib import Path

def test_fast_classification():
    """Test the fast simple classifier"""
    print("🚀 Testing Fast OpenAI Classification")
    print("=" * 50)
    
    # Find a few PDFs to test
    pdf_dirs = [
        "downloads/SEIL_Energy_India_Limited",
        "downloads/san_miguel_corporation"
    ]
    
    test_pdfs = []
    for pdf_dir in pdf_dirs:
        if os.path.exists(pdf_dir):
            pdfs = glob.glob(f"{pdf_dir}/*.pdf")
            test_pdfs.extend(pdfs[:2])  # Take 2 PDFs from each directory
            if len(test_pdfs) >= 3:  # Test with 3 PDFs max
                break
    
    if not test_pdfs:
        print("❌ No PDFs found to test")
        return
    
    print(f"📋 Testing with {len(test_pdfs)} PDFs:")
    for pdf in test_pdfs:
        print(f"   📄 {Path(pdf).name}")
    
    try:
        # Import the simple classifier
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        from simple_openai_classifier import process_pdf_list_simple
        
        print(f"\n🔍 Starting fast classification...")
        
        # Test classification
        plant_name = "Test Power Plant"
        result = process_pdf_list_simple(test_pdfs, plant_name)
        
        print(f"\n🎉 SUCCESS! Fast classification completed!")
        print(f"📊 Result: {len(result)} PDFs would be kept")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fast_classification()
