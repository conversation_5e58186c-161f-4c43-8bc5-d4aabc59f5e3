#!/usr/bin/env python3
"""
Fix NLTK data issues by downloading required packages
"""

import nltk
import ssl

def download_nltk_data():
    """Download required NLTK data packages"""
    print("🔧 Fixing NLTK data issues...")
    
    try:
        # Handle SSL certificate issues
        try:
            _create_unverified_https_context = ssl._create_unverified_context
        except AttributeError:
            pass
        else:
            ssl._create_default_https_context = _create_unverified_https_context
        
        # Download required NLTK data
        packages = [
            'punkt',
            'punkt_tab',
            'stopwords',
            'averaged_perceptron_tagger',
            'wordnet'
        ]
        
        for package in packages:
            print(f"📦 Downloading {package}...")
            try:
                nltk.download(package, quiet=False)
                print(f"✅ Successfully downloaded {package}")
            except Exception as e:
                print(f"⚠️ Failed to download {package}: {e}")
        
        print("\n🎉 NLTK data download complete!")
        
        # Test if punkt_tab is now available
        try:
            from nltk.tokenize import sent_tokenize
            test_text = "This is a test sentence. This is another sentence."
            sentences = sent_tokenize(test_text)
            print(f"✅ NLTK tokenization test successful: {len(sentences)} sentences found")
        except Exception as e:
            print(f"❌ NLTK tokenization test failed: {e}")
            
    except Exception as e:
        print(f"❌ Error downloading NLTK data: {e}")

if __name__ == "__main__":
    download_nltk_data()
