# Integrated Power Plant Annual Report Scraper

A comprehensive solution that automatically searches, downloads, validates, and classifies power plant annual reports with consolidated financial statements.

## Features

🔍 **Automated Search**: Uses AI-powered search to find relevant annual report sources
📥 **Smart PDF Downloading**: Advanced scraping with Playwright and Selenium fallbacks
🤖 **AI Validation**: Uses GPT-4o to validate and classify downloaded PDFs
📊 **Consolidated Report Detection**: Specifically identifies reports with both standalone and consolidated financials
🗑️ **Automatic Cleanup**: Removes invalid PDFs automatically
🚀 **No User Interaction Required**: Fully automated process

## Installation

### 1. Install Dependencies

```bash
# Install basic dependencies
pip install -r requirements-power-plant.txt

# Install advanced scraper dependencies
pip install playwright aiohttp aiofiles tenacity

# Install Playwright browsers
python -m playwright install
```

### 2. Set Environment Variables

Create a `.env` file with your API keys:

```bash
# Required
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Optional (improves PDF scraping)
SCRAPER_API_KEY=your_scraper_api_key_here
```

## Usage

### Interactive Mode

Run the integrated scraper interactively:

```bash
python src/integrated_power_plant_scraper.py
```

This will:
1. Ask for a power plant name
2. Automatically search for annual reports
3. Download PDFs from identified sources
4. Validate and classify PDFs using AI
5. Keep only consolidated annual reports
6. Remove invalid PDFs

### Programmatic Usage

Use the scraper in your own code:

```python
from src.integrated_power_plant_scraper import IntegratedPowerPlantScraper

# Initialize scraper
scraper = IntegratedPowerPlantScraper(download_dir="./my_downloads")

# Run automated search and validation
results = scraper.run_integrated_search_and_validation("San Miguel Corporation")

# Check results
if results["valid_pdfs"]:
    print(f"Found {len(results['valid_pdfs'])} valid consolidated annual reports")
    for pdf_path in results["valid_pdfs"]:
        print(f"Valid PDF: {pdf_path}")
else:
    print("No valid consolidated annual reports found")
```

### Example Usage Script

Run the example script to see how it works:

```bash
python src/example_usage.py
```

## How It Works

### 1. AI-Powered Search
- Uses Gemini AI to search for power plant annual reports
- Identifies relevant sources and URLs
- Handles holding company relationships

### 2. Advanced PDF Scraping
- **Primary**: ScraperAPI with Playwright for JavaScript-heavy sites
- **Fallback**: Selenium for complex interactions
- **Direct Search**: Searches for PDFs by year range if no sources found

### 3. AI Validation and Classification
Uses GPT-4o to validate each downloaded PDF by checking:
- ✅ Is it an annual report?
- ✅ Does it belong to the specified power plant?
- ✅ Does it include both standalone AND consolidated financial reports?

### 4. Automatic Cleanup
- Keeps only PDFs that pass all validation criteria
- Automatically deletes invalid PDFs
- Provides detailed reasoning for each decision

## Output Structure

Downloaded and validated PDFs are organized as:

```
downloads/
├── San_Miguel_Corporation/
│   ├── SMC_AR2025.pdf          ✅ Valid consolidated report
│   └── SMC_AR2017.pdf          ✅ Valid consolidated report
└── Pirkey/
    └── AEP_Annual_Report_2024.pdf  ✅ Valid consolidated report
```

## Results Dictionary

The `run_integrated_search_and_validation()` method returns:

```python
{
    "plant_name": "San Miguel Corporation",
    "search_successful": True,
    "pdfs_downloaded": ["path1.pdf", "path2.pdf", "path3.pdf"],
    "valid_pdfs": ["path1.pdf", "path3.pdf"],  # Only consolidated reports
    "total_downloaded": 3,
    "total_valid": 2,
    "errors": []
}
```

## Generic Approach

### Universal Power Plant Support
- **No Hardcoding**: Works with any power plant name
- **AI-Powered Discovery**: Uses AI agents to find relevant sources
- **Generic Search Patterns**: Adapts to different company structures automatically

### Fallback Mechanisms
1. If AI search fails → Direct PDF search
2. If ScraperAPI fails → Selenium fallback
3. If no PDFs found → Alternative URL patterns
4. If still no PDFs → Holding company search

## Configuration

### PDF Validation Settings
- **Model**: GPT-4o (configurable)
- **Pages Analyzed**: First 5 pages (configurable)
- **Validation Criteria**: Annual report + Correct plant + Consolidated financials

### Download Settings
- **Default Directory**: `./downloads`
- **Year Range**: Last 5 years (configurable)
- **Max PDFs per Source**: 5 (configurable)

## Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   pip install playwright aiohttp aiofiles tenacity
   python -m playwright install
   ```

2. **API Key Errors**
   - Check your `.env` file
   - Verify API keys are valid
   - Ensure sufficient API credits

3. **No PDFs Found**
   - Check if ScraperAPI key is set
   - Try with a different power plant name
   - Check if the plant has publicly available reports

### Debug Mode

Enable verbose logging by setting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## API Requirements

- **Gemini API**: For AI-powered search
- **OpenAI API**: For PDF validation (GPT-4o)
- **ScraperAPI** (Optional): For improved PDF scraping

## License

This project is licensed under the MIT License.
